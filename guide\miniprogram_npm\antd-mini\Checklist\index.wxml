<wxs
  src="./index.wxs"
  module="utils"
></wxs>
<view
  class="ant-checklist {{className || ''}}"
  style="{{style || ''}}"
>
  <view class="ant-checklist-body">
    <block
      wx:for="{{options}}"
      wx:for-index="index"
      wx:for-item="item"
      wx:key="value"
    >
      <ant-checklist-item
        checked="{{utils.getChecked(item.value, mixin.value, multiple)}}"
        item="{{item}}"
        bindchange="onChange"
      >
        <view
          slot="content"
          class="ant-checklist-item-content-box-nut"
        >
          <image
            wx:if="{{item.image}}"
            class="ant-checklist-item-image"
            src="{{item.image}}"
          ></image>
          <view class="ant-checklist-item-text {{item.image ? '' : 'ant-checklist-item-text-no-image'}}">
            <view class="ant-checklist-item-text-title {{item.disabled ? 'ant-checklist-item-text-disabled' : ''}}">{{item.title}}</view>
            <view
              wx:if="{{item.description}}"
              class="ant-checklist-item-text-description {{item.disabled ? 'ant-checklist-item-text-disabled' : ''}}"
            >{{item.description}}</view>
          </view>
        </view>
        <view slot="icon">
          <ant-icon
            type="CheckOutline"
            className="ant-checklist-item-check-icon"
          ></ant-icon>
        </view>
      </ant-checklist-item>
    </block>
  </view>
</view>