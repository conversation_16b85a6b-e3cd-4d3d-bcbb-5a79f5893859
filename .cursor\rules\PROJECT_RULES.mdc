# 项目目录结构规范 
- **后端代码专属目录**：`backend/`
  - 所有Python/Java/Go文件必须在此目录生成 
  - 包含：控制器、模型、路由等核心逻辑 
- **前端代码专属目录**：`web/`
  - 所有Vue/React/HTML/CSS文件必须在此目录生成
  - 包含：组件、页面、静态资源
- **严格禁止**：
  - 将后端代码生成到`web/`目录 
  - 将前端代码生成到`backend/`目录 
- **文件示例**：
  [后端路由示例](mdc:backend/demo/api.py) 
  [前端组件示例](mdc:web/src/views/demo/index.vue) 

  # 技术栈规范 
1. **后端技术栈**：
   - 语言：Python (Django/Flask)
   - 数据库：MySql
   - 禁止使用前端相关库 
 
2. **前端技术栈**：
   - 框架：Vue3 + TypeScript
   - UI库：Element Plus
   - 禁止调用后端私有方法 