.ant-calendar {
  color: var(--calendar-default-color, #333333);
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.ant-calendar-body {
  flex: 1;
  overflow: hidden;
}
.ant-calendar-sticky {
  position: absolute;
  top: 0px;
  left: 0px;
  max-height: 82rpx;
  width: 100%;
  overflow: hidden;
  top: 88rpx;
  z-index: 1000;
}
.ant-calendar-mark {
  height: 88rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
  font-size: 28rpx;
  padding: 0 16rpx;
}
.ant-calendar-mark .ant-calendar-mark-cell {
  flex: 1;
  text-align: center;
  width: calc((100% - 6 * 10rpx) / 7);
  margin-right: 10rpx;
}
.ant-calendar-mark .ant-calendar-mark-cell-last {
  margin-right: 0;
}
.ant-calendar-title {
  color: var(--calendar-default-color, #333333);
  font-size: 36rpx;
  height: 82rpx;
  line-height: 82rpx;
  padding-left: 40rpx;
  margin-bottom: 8rpx;
  background: var(--calendar-weekday-names-bg, #f8f8f8);
}
.ant-calendar-cells {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: stretch;
  padding: 0 16rpx;
}
.ant-calendar-cell-space {
  width: 10rpx;
  height: 110rpx;
}
.ant-calendar-cell-space-active {
  background: var(--calendar-selected-color, #1677ff);
}
.ant-calendar-cell {
  box-sizing: border-box;
  width: calc((100% - 6 * 10rpx) / 7);
  height: 110rpx;
  margin-bottom: 8rpx;
  position: relative;
}
.ant-calendar-cell-container {
  box-sizing: border-box;
  padding-top: 7rpx;
  height: 100%;
}
.ant-calendar-cell-top {
  color: var(--calendar-assist-color, #999999);
  text-align: center;
  font-size: 18rpx;
  height: 25rpx;
}
.ant-calendar-cell-top-text {
  white-space: nowrap;
}
.ant-calendar-cell-center {
  text-align: center;
  height: 45rpx;
  font-size: 32rpx;
}
.ant-calendar-cell-bottom {
  color: var(--calendar-assist-color, #999999);
  text-align: center;
  font-size: 18rpx;
  height: 25rpx;
}
.ant-calendar-cell-selected {
  background: var(--calendar-selected-color, #1677ff);
}
.ant-calendar-cell-selected-begin {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
  background: var(--calendar-selected-color, #1677ff);
}
.ant-calendar-cell-selected-end {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-selected-row-end {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}
.ant-calendar-cell-selected-row-begin {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.ant-calendar-cell-selected-begin .ant-calendar-cell-container {
  background: var(--calendar-selected-color, #1677ff);
  border-radius: 8rpx;
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-selected-end .ant-calendar-cell-container {
  background: var(--calendar-selected-color, #1677ff);
  border-radius: 8rpx;
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-selected-end .ant-calendar-cell-top {
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-selected-begin .ant-calendar-cell-top {
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-selected-end .ant-calendar-cell-bottom {
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-selected-begin .ant-calendar-cell-bottom {
  color: var(--calendar-selected-end-color, #ffffff);
}
.ant-calendar-cell-disabled {
  opacity: var(--calendar-cell-disabled-opacity, 0.4);
}
.ant-calendar-cell-hidden {
  opacity: 0;
  pointer-events: none;
}
