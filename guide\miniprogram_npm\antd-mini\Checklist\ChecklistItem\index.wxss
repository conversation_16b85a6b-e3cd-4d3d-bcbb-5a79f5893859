.ant-checklist-item {
  background-color: var(--check-list-item-background-color, #ffffff);
}
.ant-checklist-item-hover {
  background-color: var(--check-list-item-hover-background-color, #e5e5e5);
}
.ant-checklist-item-content {
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx;
  color: var(--check-list-item-content-color, #333333);
}
.ant-checklist-item-content-disabled {
  opacity: 0.4;
}
.ant-checklist-item-content-box {
  flex: 1;
}
.ant-checklist-item-content-box-nut {
  display: flex;
  align-items: center;
}
.ant-checklist-item-image {
  width: 72rpx;
  height: 72rpx;
  border-radius: 8rpx;
}
.ant-checklist-item-text {
  flex: 1;
  margin-left: 24rpx;
}
.ant-checklist-item-text-no-image {
  margin-left: 0;
}
.ant-checklist-item-text-title {
  color: var(--check-list-item-title-color, #333333);
  font-size: 34rpx;
  line-height: 48rpx;
}
.ant-checklist-item-text-description {
  font-size: 26rpx;
  color: var(--check-list-item-description-color, #999999);
  margin-top: 2rpx;
  line-height: 36rpx;
}
.ant-checklist-item-checked-disabled {
  opacity: 0.4;
}
.ant-checklist-item:last-child .ant-checklist-item-line {
  display: none;
}
.ant-checklist-item-line {
  margin-left: 24rpx;
  position: relative;
}
.ant-checklist-item-line::after {
  content: '';
  position: absolute;
  background-color: var(--check-list-item-hover-background-color, #e5e5e5);
  display: block;
  top: auto;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  transform: scaleY(0.5);
}
