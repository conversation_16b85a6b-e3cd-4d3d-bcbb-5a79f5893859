<view
  class="ant-container {{headerInBox ? 'ant-container-headerInBox' : 'ant-container-headerNotInBox'}} {{className ? className : ''}}"
  style="{{style}}"
>
  <view class="ant-container-header">
    <view class="ant-container-header-title">
      <!--display: inline-->
      <text wx:if="{{title}}">{{title}}</text>
      <slot
        name="title"
        wx:else
      ></slot>
    </view>
    <view class="ant-container-header-right">
      <slot name="headerRight"></slot>
    </view>
  </view>
  <view class="ant-container-content">
    <slot></slot>
  </view>
</view>