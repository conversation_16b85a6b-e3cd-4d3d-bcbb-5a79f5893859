# Generated by Django 4.0.8 on 2025-02-19 13:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WeiboHotSearch',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('hot_index', models.PositiveIntegerField(help_text='热搜排序')),
                ('hot_title', models.CharField(db_index=True, default='', help_text='热搜标题', max_length=200, verbose_name='热搜标题')),
                ('label_name', models.CharField(default='', help_text='热搜标签', max_length=50, verbose_name='标签')),
                ('num', models.BigIntegerField(default=0)),
                ('flag_desc', models.CharField(default='', help_text='热搜分类', max_length=100, verbose_name='热搜分类')),
                ('word_scheme', models.CharField(default='', help_text='热搜领域', max_length=200, verbose_name='热搜领域')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '微博热搜',
                'verbose_name_plural': '微博热搜',
                'db_table': 'spider_weibo_hot_search',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='TiktokLink',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('auther', models.CharField(db_index=True, default='', help_text='作者', max_length=200, verbose_name='作者')),
                ('title', models.CharField(db_index=True, default='', help_text='标题', max_length=1000, verbose_name='标题')),
                ('link', models.CharField(db_index=True, default='', help_text='视频连接', max_length=255, unique=True, verbose_name='视频连接')),
                ('likes', models.PositiveIntegerField(default=0)),
                ('comments', models.PositiveIntegerField(default=0)),
                ('collects', models.PositiveIntegerField(default=0)),
                ('shares', models.PositiveIntegerField(default=0)),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'tiktok热门视频',
                'verbose_name_plural': 'tiktok热门视频',
                'db_table': 'spider_tiktok_link',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='ShortPlay',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(db_index=True, default='', help_text='短剧名称', max_length=200, verbose_name='短剧名称')),
                ('quark_download_link', models.CharField(db_index=True, default='', help_text='夸克网盘下载地址', max_length=200, verbose_name='夸克网盘下载地址')),
                ('api_add_time', models.CharField(default='', help_text='网盘增加时间', max_length=200, verbose_name='网盘增加时间')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '短剧下载',
                'verbose_name_plural': '短剧下载',
                'db_table': 'spider_short_play',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
