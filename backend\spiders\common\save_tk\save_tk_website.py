import logging
import random
from pathlib import Path

from DrissionPage import Chromium,ChromiumOptions
from DrissionPage.errors import ElementLostError

logger = logging.getLogger(__name__)
class SaveTkWebsite:
    def __init__(self,url:str):
        self.download_dir = Path(r'E:\\datas\\files\\tiktok')
        self.options = ChromiumOptions()
        self._browser_option_setup()
        self.browser = Chromium(self.options)
        self._browser_config()
        self.base_url = 'https://savetik.net/zh-cn'
        self.download_url = url

    """浏览器启动相关配置"""
    def _browser_option_setup(self):
        """配置当前浏览器参数"""
        self.options.set_download_path(str(self.download_dir))  # 设置下载路径:cite[1]
        self.options.headless(True)
        self.options.mute(True)
        self.options.set_argument('--window-size', '1920,1081')
        self.options.set_argument('--start-maximized')  # 设置启动时最大化
        self.options.set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
        self.options.set_local_port(random.randint(1000, 9999))

    """浏览器配置"""
    def _browser_config(self):
        self.browser.set.retry_times(5)
        self.browser.set.download_path(str(self.download_dir))
    """输入url"""
    def _input_url(self,current_tab,url:str):
        self._check_browser()
        try:
            url_input_selector = '#url'
            current_tab.wait.eles_loaded(url_input_selector,timeout=10)
            current_tab.ele(url_input_selector,timeout=10)
            url_input = current_tab.ele(url_input_selector)
            url_input.input(clear=True, vals=url)
            print('成功输入url')
            logger.info('成功输入url')
            return True
        except ElementLostError as e:
            print('输入url失败')
            logger.error(f'输入url失败！{e}')
            return False
        except Exception as e:
            print('输入url失败：未知错误')
            logger.error(f'未知错误！{e}')
            return False
    """检查浏览器状态"""
    def _check_browser(self):
        if self.browser:
            print('浏览器正常')
            logger.info('浏览器正常')
        else:
            logger.error('浏览器异常')
            print('浏览器异常')
            return False

    """解析url"""
    def _parse_url(self,current_tab):
        self._check_browser()
        try:
            parse_url_button_selector = '@@tag()=button@@type=submit'
            current_tab.wait.eles_loaded(parse_url_button_selector,timeout=10)
            current_tab.ele(parse_url_button_selector,timeout=10).click()
            print('解析成功')
            logger.info('解析成功')
            return True
        except ElementLostError as e:
            print(f'解析地址失败{e}')
            logger.error(f'解析地址失败{e}')
            return False
        except Exception as e:
            print('解析地址失败：未知异常')
            logger.error(f'解析地址失败：未知异常{e}')
            return False

    """点击下载"""
    def _download(self,current_tab):
        self._check_browser()
        try:
            download_button_selector = '@@tag()=div@@class=mt-4'
            # download_button_selector = '@@tag()=a@@class=flex items-center justify-center w-full gap-2 p-2 mt-2 text-center text-white rounded bg-primary'
            current_tab.wait.eles_loaded(download_button_selector,timeout=10)
            mission = current_tab.ele(download_button_selector,timeout=10).click.to_download()
            logger.info('点击开始下载按钮成功')
            print('点击开始下载按钮成功')
            mission.wait()
            if mission:
                logger.info(mission.is_done)
                # mission.wait()
                logger.info(f'文件下载完成: {mission}')
                return True
            else:
                logger.error(f'文件下载失败: {mission}')
                return False
        except ElementLostError as e:
            print(f'下载失败（获取下载按钮失败）{e}')
            logger.error(f'下载失败（获取下载按钮失败）{e}')
            return False
        except Exception as e:
            print(f'下载失败（未知异常）{e}')
            logger.error(f'下载失败（未知异常）{e}')
            return False
        finally:
            print('关闭浏览器')
            self.browser.quit()

    def download_line(self):
        current_tab = self.browser.latest_tab
        current_tab.get(self.base_url)
        if all([
            self._input_url(current_tab,self.download_url),
            self._parse_url(current_tab),
            self._alert_url(current_tab),
            self._download(current_tab)
        ]):
            logger.info('下载成功')
            return True
        else:
            logger.error('下载失败（具体阶段请查看日志）')
            return False
    """判断解析url是否出现警告"""
    def _alert_url(self, current_tab):
        self._check_browser()
        parse_url_button_selector = 'tag:div@class=alert'
        current_tab.wait.eles_loaded(parse_url_button_selector, timeout=15)
        ele = current_tab.ele(parse_url_button_selector, timeout=10)
        if ele:
            print('出现警告')
            return False
        else:
            print("没出现警告")
            return True



if __name__ == '__main__':
    saveTkWebsite = SaveTkWebsite('https://www.tiktok.com/@king_min_su/video/7454025651599822087')
    saveTkWebsite.download_line()
