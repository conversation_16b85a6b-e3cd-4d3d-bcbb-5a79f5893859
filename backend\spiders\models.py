from django.db import models

from utils.models import CoreModel


# Create your models here.
class TiktokHotSearch(CoreModel):
    hot_index = models.PositiveIntegerField(help_text='热搜排序')
    hot_title = models.CharField(max_length=200, db_index=True, verbose_name='热搜标题',help_text="热搜标题",default='')
    hot_desc = models.TextField(verbose_name='热搜描述',help_text='热搜描述',default='')
    hot_url = models.CharField(max_length=500, verbose_name='热搜链接',help_text='热搜链接',default='')
    hot_type = models.CharField(max_length=50, verbose_name='热搜类型',help_text='热搜类型',default='')
    hot_value = models.CharField(max_length=50, verbose_name='热度值',help_text='热度值',default='')
    create_datetime = models.DateTimeField(auto_now_add=True,verbose_name='创建时间')
    update_datetime = models.DateTimeField(auto_now=True,verbose_name='更新时间')

    class Meta:
        db_table = "spider_tiktok_hot_search"
        verbose_name = '抖音热搜'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)


class WeiboHotSearch(CoreModel):
    hot_index = models.PositiveIntegerField(help_text='热搜排序')
    hot_title = models.CharField(max_length=200, db_index=True, verbose_name='热搜标题',help_text="热搜标题",default='')
    label_name = models.CharField(max_length=50,verbose_name='标签',help_text='热搜标签',default='')
    num = models.BigIntegerField(default=0)
    flag_desc = models.CharField(max_length=100, verbose_name='热搜分类',help_text="热搜分类",default='')
    word_scheme = models.CharField(max_length=200,  verbose_name='热搜领域',help_text="热搜领域",default='')
    create_datetime = models.DateTimeField(auto_now_add=True,verbose_name='创建时间')
    update_datetime = models.DateTimeField(auto_now=True,verbose_name='更新时间')

    class Meta:
        db_table = "spider_weibo_hot_search"
        verbose_name = '微博热搜'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)



class ShortPlay(CoreModel):
    name = models.CharField(max_length=200, db_index=True, verbose_name='短剧名称',help_text="短剧名称",default='')
    quark_download_link =  models.CharField(max_length=200, db_index=True, verbose_name='夸克网盘下载地址',help_text="夸克网盘下载地址",default='')
    api_add_time = models.CharField(max_length=200,verbose_name='网盘增加时间',help_text="网盘增加时间",default='')


    class Meta:
        db_table = "spider_short_play"
        verbose_name = '短剧下载'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

class TiktokLink(CoreModel):
    auther = models.CharField(max_length=200, db_index=True, verbose_name='作者',help_text="作者",default='')
    title =  models.CharField(max_length=1000, db_index=True, verbose_name='标题',help_text="标题",default='')
    link =  models.CharField(max_length=255,unique=True, db_index=True, verbose_name='视频连接',help_text="视频连接",default='')
    likes = models.PositiveIntegerField(default=0)
    comments = models.PositiveIntegerField(default=0)
    collects = models.PositiveIntegerField(default=0)
    shares = models.PositiveIntegerField(default=0)
    download_status = models.PositiveIntegerField(default=0)
    file_delete_status = models.PositiveIntegerField(default=0)
    dy_download_status = models.PositiveIntegerField(default=0)




    class Meta:
        db_table = "spider_tiktok_link"
        verbose_name = 'tiktok热门视频'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)