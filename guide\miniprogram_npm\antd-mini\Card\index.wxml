<view class="ant-card {{className ? className : ''}}">
  <ant-sticky
    sticky="{{headSticky}}"
    check="{{headSticky}}"
    top="{{stickyTop}}"
    transparentTitle="{{stickyTransparentTitle}}"
    bindstickychange="handleStickyChange"
  >
    <view
      wx:if="{{title}}"
      class="ant-card-header"
      bindtap="handleTapCardHeader"
    >
      <view class="ant-card-header-title">{{title}}</view>
      <view
        wx:if="{{operateText}}"
        class="ant-card-header-right-link"
        bindtap="handleTapLink"
      >{{operateText}}<ant-icon type="RightOutline"></ant-icon>
      </view>
      <view
        wx:elif="{{needFold}}"
        class="ant-card-header-right-link"
        bindtap="handleTapFoldBtn"
      >
        <text class="ant-card-header-fold-btn">{{finalFoldStatus ? '展开' : '收起'}}</text>
        <ant-icon
          class="ant-card-header-fold-icon"
          type="{{finalFoldStatus ? 'DownOutline' : 'UpOutline'}}"
        ></ant-icon>
      </view>
    </view>
  </ant-sticky>
  <view
    wx:if="{{!finalFoldStatus}}"
    class="ant-card-divider {{divider ? 'ant-card-divider-with-line' : ''}}"
  ></view>
  <!--卡片内容插槽-->
  <view class="ant-card-content {{finalFoldStatus ? 'ant-card-content-collapse' : ''}}">
    <slot></slot>
  </view>
</view>