[supervisord]
nodaemon=true

[program:django]
command=python manage.py runserver 0.0.0.0:8000
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/django.err.log
stdout_logfile=/var/log/django.out.log

[program:celery-worker]
command=celery -A fuadmin worker --loglevel=info  -P gevent
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/celery-worker.err.log
stdout_logfile=/var/log/celery-worker.out.log

[program:celery-beat]
command=celery -A fuadmin beat --loglevel=info
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/celery-beat.err.log
stdout_logfile=/var/log/celery-beat.out.log
