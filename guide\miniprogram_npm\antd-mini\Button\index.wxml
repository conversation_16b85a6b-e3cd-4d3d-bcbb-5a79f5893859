<wxs
  src="./index.wxs"
  module="utils"
></wxs>
<button
  formType="{{formType}}"
  hoverClass="{{utils.getHoverClass(loading, type, activeClassName)}}"
  scope="{{scope}}"
  bindgetuserinfo="onGetUserInfo"
  bindcontact="onContact"
  bindgetphonenumber="onGetPhoneNumber"
  bindgetrealtimephonenumber="onGetRealTimePhoneNumber"
  binderror="onError"
  bindlaunchapp="onLaunchApp"
  bindopensetting="onOpenSetting"
  bindagreeprivacyauthorization="onAgreePrivacyAuthorization"
  bindchooseavatar="onChooseAvatar"
  publicId="{{publicId}}"
  openType="{{openType}}"
  class="ant-button {{inline ? 'ant-button-inline ' + utils.getClass(size) : ''}} {{'ant-button-' + type + (danger ? '-danger' : utils.isAide(aide, type) ? '-aide' : '')}} {{disabled || loading ? 'ant-button-disabled' : ''}} {{className ? className : ''}}"
  style="{{style}}"
>
  <view class="ant-button-wrap">
    <ant-icon
      wx:if="{{!!icon}}"
      type="{{icon}}"
    ></ant-icon>
    <view class="ant-button-content-text {{icon ? 'ant-button-content-text-margin' : ''}}">
      <slot></slot>
      <view
        wx:if="{{!inline && subText}}"
        class="ant-button-content-subtext"
      >{{subText}}</view>
    </view>
    <view
      wx:if="{{loading}}"
      class="ant-button-content-loading-container"
    >
      <loading
        type="mini"
        color="currentColor"
        className="ant-button-content-loading"
      ></loading>
    </view>
  </view>
</button>