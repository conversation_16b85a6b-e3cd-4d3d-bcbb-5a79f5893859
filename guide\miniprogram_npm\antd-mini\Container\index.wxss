.ant-container {
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}
.ant-container-headerInBox {
  background-color: var(--container-background-color, #ffffff);
}
.ant-container-headerInBox .ant-container-header {
  padding: 0 24rpx;
}
.ant-container-headerInBox .ant-container-header .ant-container-header-title,
.ant-container-headerInBox .ant-container-header .ant-container-header-right {
  position: relative;
}
.ant-container-headerInBox .ant-container-header .ant-container-header-title::after,
.ant-container-headerInBox .ant-container-header .ant-container-header-right::after {
  content: '';
  position: absolute;
  background-color: #e5e5e5;
  display: block;
  top: auto;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  transform: scaleY(0.5);
}
.ant-container-content {
  padding: 24rpx;
  background-color: var(--container-background-color, #ffffff);
  border-radius: 16rpx;
  color: var(--container-header-color, #333333);
}
.ant-container-header {
  display: flex;
}
.ant-container-header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--container-header-color, #333333);
  display: flex;
  align-items: center;
  height: 96rpx;
  flex: 1;
}
.ant-container-header-title:empty {
  display: none;
}
.ant-container-header-right {
  color: var(--container-color-text-assist, #999999);
  display: flex;
  align-items: center;
  height: 96rpx;
  justify-content: flex-end;
}
.ant-container-header-right:empty {
  display: none;
}
.ant-container-header .ant-container-header-title:empty + .ant-container-header-right:not(:empty) {
  flex: 1;
}
