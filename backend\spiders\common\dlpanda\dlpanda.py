import logging
import random
from pathlib import Path

from DrissionPage import Chromium,ChromiumOptions
from DrissionPage.errors import ElementLostError

logger = logging.getLogger(__name__)
class DLPanda:
    def __init__(self):
        self.download_dir = Path(r'E:\\datas\\files\\tiktok')
        self.options = ChromiumOptions()
        self._browser_option_setup()
        self.browser = Chromium(self.options)
        self._browser_config()
        self.base_url = 'https://dlpanda.com/'
        self.download_url = ''
        self.tab = self.browser.latest_tab

    """浏览器启动相关配置"""
    def _browser_option_setup(self):
        """配置当前浏览器参数"""
        self.options.set_download_path(str(self.download_dir))  # 设置下载路径:cite[1]
        self.options.headless(False)
        self.options.mute(True)
        self.options.set_argument('--start-maximized')  # 设置启动时最大化
        self.options.set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
        self.options.set_local_port(random.randint(1000, 9999))

    """浏览器配置"""
    def _browser_config(self):
        self.browser.set.retry_times(5)
        self.browser.set.download_path(str(self.download_dir))

    """初始化页面"""
    def init_page(self):
        try:
            response = self.tab.get(self.base_url)
            print(response)
            return True
        except ElementLostError as e:
            print(f'初始化页面失败{e}')
            print(e)
            return False
        except Exception as e:
            print(f"初始化异常{e}")
            print(e)
            return False
    """公共处理方法"""
    def _common_selector_handler(self,selector,case:str,timeout:int,value:str):
        try:
            _selector = selector # 'tag:input@id:url'
            self.tab.wait.eles_loaded(_selector, timeout)
            ele = self.tab.ele(selector)
            if case == 'click':
                ele.click()
                return True
            if case == 'input':
                ele.input(clear=True, vals=value)
                return True
            if case == 'download':
                mission = ele.click.to_download()
                print(mission)
                if mission:
                    logger.info(f'文件下载完成: {mission}')
                    return True
                else:
                    logger.info(f'文件下载失败: {mission}')
                    return False
        except ElementLostError as e:
            print(selector + f'异常：{e}')
            print(e)
            return False
        except Exception as e:
            print(selector + f"{e}")
            print(e)
            return False

    """下载链路"""
    def download_by_url(self,url:str):
        """输入下载链接"""
        input_selector = 'tag:input@id:url'
        self._common_selector_handler(input_selector,'input',10,url)
        url_parse_selector = 'tag:button@type:submit'
        self._common_selector_handler(url_parse_selector,'click',10,'')
        download_selector = 'tag:button@id:download-video-btn'
        self._common_selector_handler(download_selector,'click',10,'')


if __name__ == '__main__':
    dlPanda = DLPanda()
    dlPanda.init_page()
    dlPanda.download_by_url('https://www.tiktok.com/@amthuctrungquoc2025/video/7455588254780296466')