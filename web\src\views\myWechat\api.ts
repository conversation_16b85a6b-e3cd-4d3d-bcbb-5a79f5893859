
import { getWechatMiniList, createWechatMini, updateWechatMini, deleteWechatMini, getWechatMiniById } from '/@/api/MyMiniProgram';
import { MyWechatMiniParams } from '/@/api/model/myWechatMiniModel';

/**
 * 获取小程序列表
 */
export const getList = (params?: MyWechatMiniParams) => {
  return getWechatMiniList(params);
};

/**
 * 保存或更新小程序
 */
export const createOrUpdate = (params: MyWechatMiniParams, isUpdate: boolean) => {
  if (isUpdate) {
    return updateWechatMini(params.id!, params);
  } else {
    return createWechatMini(params);
  }
};

/**
 * 导入数据
 * TODO: 实现导入功能
 */
export const importData = (params: { path: string }) => {
  // 暂时使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 500);
  });
};

/**
 * 导出数据
 * TODO: 实现导出功能
 */
export const exportData = () => {
  // 暂时使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ data: new Blob(['模拟数据']) });
    }, 500);
  });
};

/**
 * 删除小程序
 */
export const deleteItem = (id: number) => {
  return deleteWechatMini({ id });
};

/**
 * 获取单个小程序详情
 */
export const getItem = (id: number) => {
  return getWechatMiniById({ id });
};
