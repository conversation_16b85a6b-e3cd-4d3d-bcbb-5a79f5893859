import threading
from os import access

import requests
import time

url = "http://**************/dms/"
success_count = 0
_access_token = None


import requests




def  get_access_token():
    try:
        base_url = "http://**************/data-exc-admin/authentication/token"
        payload = 'sysU=vpo1lMVkBmwj5gfSTHqxe5WKPPr%2FU0WyzY%2Fwo0mkoa4%3D&sysP=04bf262612f7dad1b2fea69f9dbfeb3bda423f78cfd435744787f33b85d45e483c5a4302add6839ad41f4931d76431ebdb088599708e91b9079d37025f8244b4385be5a9d5f61a91da3724170265c468a1367d63a7b88c21c0b7812266a6acd4e7ea21045317a43e70e9c301e074d5ee70&sysT=01&isSecret=1&auth=Basic%20YjJlZDZmNDctMWE2OC00MTgxLTliN2UtYzg5MWNhNzgxOTNkOjk4NGU2M2JjLTQxMTAtNGMwNC1hOWUyLWE2ODhlZGIwMzk5ZQ%3D%3D'
        headers = {
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Accept': '*/*',
            'Host': '**************',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        response = requests.request("POST", base_url, headers=headers, data=payload)
        _access_token = response.json().get("returnData").get("successData").get('data')
        print(_access_token)

    except Exception as e:
        print(e)

def worker():
    global success_count
    while True:
        try:
            if _access_token:
                pass
            else:
                print("请获取token！")

        except Exception as e:
            print("登陆异常")
            print(e)


get_access_token()

# threads = []
# for _ in range(100):  # 启动100个线程
#     t = threading.Thread(target=worker)
#     t.daemon = True
#     threads.append(t)
#     t.start()

# 运行30秒后统计结果
time.sleep(30)
print(f"总成功请求数：{success_count}")
print(f"平均QPS：{success_count / 30:.2f}")