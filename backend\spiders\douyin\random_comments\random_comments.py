import random
import time

from DrissionPage.errors import Wrong<PERSON><PERSON>rror, ElementLostError
from paddle.jit.dy2static.export_subgraph import pir_exporter
from win32comext.shell.demos.IUniformResourceLocator import property_ids

from spiders.douyin.upload_to_douyin.upload_to_douyin import DouYin


class RandomComments(DouYin):
    """初始化"""
    def __init__(self) -> None:
        super().__init__()
        self.comment_counts = 100
        self.base_url = 'https://www.douyin.com/?recommend=1'
        self.tab = self.browser.latest_tab

    """浏览器启动相关配置"""
    def _browser_option_setup(self):
        """配置当前浏览器参数"""
        self.options.headless(False)
        self.options.mute(True)
        self.options.set_argument('--start-maximized')  # 设置启动时最大化
        # self.options.set_argument('--window-size', '1920,1080')
        self.options.set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
        self.options.set_local_port(9876)

    """浏览器配置"""
    def _browser_config(self):
        self.browser.set.retry_times(5)

    """抖音PC状态"""
    def _douyin_pc_status(self):
        try:
            douyin_header_menuCt_selector = 'tag:div@id=douyin-header-menuCt'
            if self.tab.wait.eles_loaded(douyin_header_menuCt_selector,timeout=5):
                return True
            else:
                return False
        except ElementLostError as e:
            print(f"找不大元素{e}")
            return False

    """初始化页面"""
    def init_page(self):
        try:
            _accept_button_selector = 'tag:span@@text():我知道了'
            if self.try_cookie_login():
                print("cookie 登录成功")
                self.tab.get(self.base_url)
                ele = self.tab.ele(_accept_button_selector,timeout=5)
                print(f"我知道了状态：{ele}")
                if ele :
                    print("点击我知道了")
                    ele.click()
                else:
                    print("未找到弹窗1")
                return self._douyin_pc_status()
            else:
                print("cookie登录失败!")
                return False
        except WrongURLError as e:
            print(f'链接异常{e}')
            self.browser.quit()
            print(e)
            return False
        except Exception as e:
            print(f"访问首页未知异常{e}")
            self.browser.quit()
            print(e)
            return False

    """评论按钮"""
    def _comment_button(self):
        try:
            comment_button_selector = 'tag:div@class=kT7icnwc' # 点击评论按钮
            self._check_() # 是否为直播
            if self.tab.wait.eles_loaded(comment_button_selector,timeout=5):
                print("找到评论按钮,开始点击")
                self.tab.ele(comment_button_selector,timeout=5).click()
                return True
            else:
                print("没找到评论按钮，GG")
                return False
            # video_side_cart_selector = 'tag:div@class=videoSideCard'  # 评论列表面板
            # div_content_selector = 'tag:div@data-contents=true'
        except ElementLostError as e:
            print(e)
            return False
        except Exception as e:
            print(e)
            return False

    def _check_(self):
        try:
            _message_selector = 'tag:div@class=EhebFia2'
            _next_video_selector = 'tag:div@data-e2e=video-switch-next-arrow'
            if self.tab.wait.eles_loaded(_message_selector,timeout=5):
                print("直播提示存在，是直播，开始点击下个视频按钮！")
                if self.tab.wait.eles_loaded(_next_video_selector,timeout=5):
                    self.tab.ele(_next_video_selector,timeout=5).click()
                    print("点击下一个视频按钮成功")
                    return True
                else:
                    print("点击下一个视频按钮失败")
                    return  False
            else:
                print("不是直播！！！")
                return True
        except ElementLostError as e:
            print("找不到元素")
            print(e)
            return False
        except Exception as e:
            print("找不到元素")
            print(e)
            return False

    """评论方法"""
    def random_comment(self):
        _comment_count = 0
        if _comment_count < self.comment_counts:
            if self._comment_button():
                print("发送评论应该成功了")
                self._send_comment()
            else:
                print("发送评论GG了")

            _comment_count = _comment_count + 1
        else:
            print('到达评论目标次数,准备退出！')
            print("10秒后退出浏览器")
            time.sleep(10)
        # self.browser.quit()

    def _scroll_view(self):
        self.tab.scroll(10)

    def quit(self):
        try:
            self.browser.quit()
            return True
        except Exception as e:
            print("关闭浏览器异常")
            print(e)
            return False
    """"""
    def _send_comment(self):
        comment_input_selector = 'tag:div@class=FL8ObPBR' # 输入评论框内容
        send_comment_button_selector = 'tag:div@class=videoSideCard' # 点击发送评论按钮
        try:
            if self.tab.wait.eles_loaded(comment_input_selector,timeout=5):
                self.tab.ele(comment_input_selector, timeout=5).check()
                self.tab.ele(comment_input_selector, timeout=5).input("新人，求各位爸爸来我主页看看点点赞，谢谢爸！！-----自动的,有需求可联系\n")
                print("输入评论内容成功！！")
                return True
                # if self.tab.wait.eles_loaded(send_comment_button_selector,timeout=5):
                #     self.tab.ele(send_comment_button_selector,5).click()
                #     print("点击发送评论成功")
                #     return True
                # else:
                #     print("找不到发送评论按钮咯")
                #     return False
            else:
                print("找不到输入评论的框框呀！")
                return False
        except ElementLostError as e:
            print(f"找不到输入评论的元素{e}")
            return False
        except Exception as e:
            print(f"评论输入异常{e}")
            return False




"""测试"""
if __name__ == '__main__':
    """评论类"""
    random_comments = RandomComments()
    # """cookie登录"""
    # random_comments.try_cookie_login()
    """初始化"""
    if random_comments.init_page():
        random_comments.random_comment()
    else:
        print("初始化页面失败并在十秒后关闭浏览器，请排查！！！")
        time.sleep(10)
        random_comments.quit()
