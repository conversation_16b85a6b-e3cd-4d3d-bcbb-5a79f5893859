var jaJP = {
    // locales for all components
    locale: 'ja-JP',
    global: {
        placeholder: '選択してください',
        emptyText: 'データはありません。',
        okText: '確定',
        cancelText: 'キャンセル',
    },
    input: {
        placeholder: '入力してください',
    },
    calendar: {
        weekdayNames: [
            '月曜日',
            '火曜日',
            '水曜日',
            '木曜日',
            '金曜日',
            '土曜日',
            '日曜日',
        ],
        format: 'MM/YYYY',
        today: '今日です',
        start: '開始',
        end: '終了',
        startAndEnd: '開始/終了',
    },
    rangePicker: {
        startPlaceholder: '選択が開始されていません',
        endPlaceholder: '未完成の選択',
    },
    guideTour: {
        gotItText: 'わかりました',
        nextStepText: '次のステップ',
        prevStepText: '前のステップ',
        jumpText: 'スキップ',
    },
    imageUpload: {
        uploadingText: 'アップロード中です',
        uploadfailedText: 'アップロードに失敗しました',
    },
    pageContainer: {
        failed: {
            title: 'ページにいくつかの小さな問題が発生しています',
            message: '後で試してみます',
        },
        disconnected: {
            title: 'ネットワークが少し混雑しています',
            message: '指を動かして修正してください',
        },
        empty: {
            title: 'ここには何もありません',
            message: '他のものをチェックしてください',
        },
        busy: {
            title: '前方が渋滞しています',
            message: 'リフレッシュしてみてください',
        },
    },
};
export default jaJP;
