server {
    listen 8080;  
    server_name localhost;
    # ssl_certificate /www/wwwroot/fuadmin/ssl/server.crt;  
    # ssl_certificate_key /www/wwwroot/fuadmin/ssl/server.rsa;  
    # ssl_session_timeout 5m;  
    # ssl_protocols SSLv2 SSLv3 TLSv1;  
    # ssl_ciphers ALL:!ADH:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP;  
    # ssl_prefer_server_ciphers on;  
    client_max_body_size 100M;
    location / {
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        set_real_ip_from 0.0.0.0/0;
        real_ip_header X-Forwarded-For;
        root /usr/share/nginx/html;
        index  index.html index.php index.htm;
    }
    
        location /basic-api {
        proxy_set_header Host $http_host;
        proxy_set_header  X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        set_real_ip_from 0.0.0.0/0;
        real_ip_header X-Forwarded-For;
        rewrite ^/basic-api/(.*)$ /$1 break;  #重写
        proxy_pass http://**********:8000; # 设置代理服务器的协议和地址
     }
 }