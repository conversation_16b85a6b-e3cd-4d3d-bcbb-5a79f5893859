# crawler/tasks.py
from time import sleep

from django.db import transaction

from spiders.tk.deepseek01.core.browsers import BaseDrissionTask
from spiders.tk.task_line import logger


class TikTokCrawler(BaseDrissionTask):
    VIDEO_DETECT_CSS = 'div[data-e2e="recommend-list"] a'

    def _collect_links(self, url, mode):
        try:
            self.browser.get(url)
            for _ in range(10):  # 滚动10次
                self.browser.scroll.down(500)
                sleep(2)
                links = [a.attr('href') for a in self.browser.eles(self.VIDEO_DETECT_CSS)]
                self._save_links(links, mode)
        except Exception as e:
            logger.error(f"爬取失败: {str(e)}")
            raise
        finally:
            self.cleanup()

    def _save_links(self, links, source_type):
        with transaction.atomic():
            for link in links:
                VideoLink.objects.update_or_create(
                    url=link,
                    defaults={'source_type': source_type, 'status': 'pending'}
                )


class RecommendCrawlerTask(TikTokCrawler):
    def execute(self):
        self._collect_links("https://www.tiktok.com/", "recommend")


class ExploreCrawlerTask(TikTokCrawler):
    def execute(self):
        self._collect_links("https://www.tiktok.com/explore", "explore")