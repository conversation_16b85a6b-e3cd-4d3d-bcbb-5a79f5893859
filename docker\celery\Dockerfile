FROM python:3.9-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /backend

# 复制依赖文件
COPY ./backend/requirements.txt requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt

# 复制项目文件
COPY ./backend .

# 复制环境配置文件
COPY ./backend/conf/docker.py conf/env.py

# 创建日志目录
RUN mkdir -p /backend/logs/celery

# 设置环境变量
ENV DJANGO_SETTINGS_MODULE=fuadmin.settings
ENV C_FORCE_ROOT=true

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health/')" || exit 1

# 启动命令 - 只运行微博任务
CMD ["sh", "-c", "celery -A fuadmin worker --loglevel=info --concurrency=2 --queues=weibo_tasks"]