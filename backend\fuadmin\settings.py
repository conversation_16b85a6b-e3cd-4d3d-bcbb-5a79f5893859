"""
Django settings for fuadmin project.

Generated by 'django-admin startproject' using Django 4.0.4.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""
import datetime
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import environ
from django.core.management import templates

# 初始化 environ
env = environ.Env()
# 读取 .env 文件
environ.Env.read_env()


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = locals().get('INFO', True)
ALLOWED_HOSTS = locals().get('ALLOWED_HOSTS', ['*'])
DEMO = locals().get('DEMO', False)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuadmin.settings')
# Application definition


# 微信配置
WECHAT_APPID = 'wx5fd8eebdcce9e914'
WECHAT_SECRET = '9a5f92f6022136e1154fc6867cfa7886'

# DRF配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ]
}

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_celery_beat',
    'django_celery_results',
    'system',
    'spiders',
    'Stars',
    'analysis',
    'demo',
    'generator',
    'rest_framework',
    'rest_framework_simplejwt',
    'Mywechatmini'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'utils.middleware.ApiLoggingMiddleware',

]

ROOT_URLCONF = 'fuadmin.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'fuadmin.wsgi.application'

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTH_USER_MODEL = 'system.Users'
USERNAME_FIELD = 'username'
ALL_MODELS_OBJECTS = []  # 所有app models 对象

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases
# 数据库配置
DATABASE_TYPE  = env('DATABASE_TYPE')
if DATABASE_TYPE == "MYSQL":
    # Mysql数据库
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.mysql",
            "HOST": env.str('DATABASE_HOST'),
            "PORT": env('DATABASE_PORT'),
            "USER": env.str('DATABASE_USER'),
            "PASSWORD": env('DATABASE_PASSWORD'),
            "NAME": env('DATABASE_NAME'),
            "charset":"utf8mb4",
        }
    }
elif DATABASE_TYPE == "SQLSERVER":
    # SqlServer数据库
    DATABASES = {
        "default": {
            "ENGINE": "mssql",
            "HOST": env.str('DATABASE_HOST'),
            "PORT": env('DATABASE_PORT'),
            "USER": env.str('DATABASE_USER'),
            "PASSWORD": env('DATABASE_PASSWORD'),
            "NAME": env('DATABASE_NAME'),
            # 全局开启事务，绑定的是http请求响应整个过程
            'ATOMIC_REQUESTS': True,
            'OPTIONS': {
                'driver': 'ODBC Driver 17 for SQL Server',
            },
        }
    }
elif DATABASE_TYPE == "POSTGRESQL":
    # POSTGRESQL
    DATABASES = {
        "default": {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            "HOST": env.str('DATABASE_HOST'),
            "PORT": env('DATABASE_PORT'),
            "USER": env.str('DATABASE_USER'),
            "PASSWORD": env('DATABASE_PASSWORD'),
            "NAME": env('DATABASE_NAME'),
            # 全局开启事务，绑定的是http请求响应整个过程
            'ATOMIC_REQUESTS': True,
        }
    }
else:
    # sqlite3 数据库
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
            'OPTIONS': {
                'timeout': 20,
            },
        }
    }

# 缓存配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://:{env.str('REDIS_PASSWORD', '')}@{env.str('REDIS_HOST', 'localhost')}:{env.str('REDIS_PORT', 6366)}/1",
        "TIMEOUT": None,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    },
}


# # ================================================= #
# # ********************* 日志配置 ******************* #
# # ================================================= #
# # log 配置部分BEGIN #
SERVER_LOGS_FILE = os.path.join(BASE_DIR, "logs", "server.log")
ERROR_LOGS_FILE = os.path.join(BASE_DIR, "logs", "error.log")
LOGS_FILE = os.path.join(BASE_DIR, "logs")
if not os.path.exists(os.path.join(BASE_DIR, "logs")):
    os.makedirs(os.path.join(BASE_DIR, "logs"))

# 格式:[2020-04-22 23:33:01][microservice.apps.ready():16] [INFO] 这是一条日志:
# 格式:[日期][模块.函数名称():行号] [级别] 信息
STANDARD_LOG_FORMAT = (
    "[%(asctime)s][%(name)s.%(funcName)s():%(lineno)d] [%(levelname)s] %(message)s"
)
CONSOLE_LOG_FORMAT = (
    "[%(asctime)s][%(name)s.%(funcName)s():%(lineno)d] [%(levelname)s] %(message)s"
)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {"format": STANDARD_LOG_FORMAT},
        "console": {
            "format": CONSOLE_LOG_FORMAT,
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "file": {
            "format": CONSOLE_LOG_FORMAT,
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": SERVER_LOGS_FILE,
            "maxBytes": 1024 * 1024 * 100,  # 100 MB
            "backupCount": 5,  # 最多备份5个
            "formatter": "standard",
            "encoding": "utf-8",
        },
        "error": {
            "level": "ERROR",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": ERROR_LOGS_FILE,
            "maxBytes": 1024 * 1024 * 100,  # 100 MB
            "backupCount": 3,  # 最多备份3个
            "formatter": "standard",
            "encoding": "utf-8",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "console",
        },

    },
    "loggers": {
        "": {
            "handlers": ["console", "error", "file"],
            "level": "INFO",
        },
        "django": {
            "handlers": ["console", "error", "file"],
            "level": "INFO",
            "propagate": False,
        },
        'django.db.backends': {
            'handlers': ["console", "error", "file"],
            'propagate': False,
            'level': "INFO"
        },
        "uvicorn.error": {
            "level": "INFO",
            "handlers": ["console", "error", "file"],
        },
        "uvicorn.access": {
            "handlers": ["console", "error", "file"],
            "level": "INFO"
        },
    },
}


# celery 配置
CELERY_BROKER_URL = f"redis://:{env.str('REDIS_PASSWORD', '')}@{env.str('REDIS_HOST', 'localhost')}:{env.str('REDIS_PORT', 6366)}/2"
#redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:{REDIS_PORT}
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_BACKEND = f"redis://:{env.str('REDIS_PASSWORD', '')}@{env.str('REDIS_HOST', 'localhost')}:{env.str('REDIS_PORT', 6366)}/3"
# Redis 作为 result backend)
CELERY_TIMEZONE = 'UTC'
# 明确指定任务模块，避免自动发现时导入不需要的模块
CELERY_IMPORTS = (
    'spiders.tasks',
    'system.tasks',
)

# Celery 日志配置
CELERYD_LOG_FILE = os.path.join(BASE_DIR, 'logs', 'celery', 'celery_worker.log')
CELERYD_LOG_LEVEL = 'INFO'
CELERYBEAT_LOG_FILE = os.path.join(BASE_DIR, 'logs', 'celery', 'celery_beat.log')
CELERYBEAT_LOG_LEVEL = 'INFO'



SIMPLE_JWT = {
     'TOKEN_OBTAIN_SERIALIZER': 'wechatUser.serializers.CustomTokenSerializer',   # 自定义响应
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30)  # 适配小程序长期会话
}
# token 有效时间 时 分 秒
TOKEN_LIFETIME = 12 * 60 * 60
# TOKEN_LIFETIME = 50

# 接口白名单，不需要授权直接访问
WHITE_LIST = ['/api/system/userinfo', '/api/system/permCode', '/api/system/menu/route/tree', '/api/system/user/*',
              '/api/system/user/set/repassword']

# 接口日志记录
API_LOG_ENABLE = True
API_LOG_METHODS = ['POST', 'GET', 'DELETE', 'PUT']
API_MODEL_MAP = {}

# 初始化需要执行的列表，用来初始化后执行
INITIALIZE_RESET_LIST = []




