import requests
from locust import HttpUser, task, between
from bs4 import BeautifulSoup



# 获取 token 的函数
def get_token():
    url = 'http://**************/data-exc-admin/authentication/token'
    payload = 'sysU=DneqHwQJ%2BKcpguDo0UVQiw%3D%3D&sysP=04e70d8ede911d82ef98f47e18395596577bb12b3c64c15bf8f8f7d6d3ffa98ad9893087fcf2b8a73d7c6b63167783bdfec9808bccb534e912654a8e3b1d27a458b4e8f36d440b2fac12a577c9c83e5a7582fdcf313215595283aed33e9dd4dedc8d726cfe4535231608b605d25d5989b6&sysT=01&isSecret=1&auth=Basic%20YjJlZDZmNDctMWE2OC00MTgxLTliN2UtYzg5MWNhNzgxOTNkOjk4NGU2M2JjLTQxMTAtNGMwNC1hOWUyLWE2ODhlZGIwMzk5ZQ%3D%3D'
    headers = {
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Accept': '*/*',
        'Host': '**************',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    response = requests.post(url, data=payload, headers=headers)
    if response.status_code == 200:
        print("获取token成功")
        token = response.json().get("returnData").get("successData").get('data')
        if token:
            print("获取token成功：" + token)
        else:
            print("获取token失败")
        return token
    else:
        print("获取token失败")
        return None


# 定义 Locust 用户类
class MyUser(HttpUser):
    # 用户执行任务的间隔时间 
    wait_time = between(1, 3)


    def on_start(self):
        # 在用户开始执行任务前获取 token 
        self.token = get_token()
        if self.token:
            self.headers = {
                'Authorization': f'Bearer {self.token}'
            }
            self.client.cookies.set(
                name="access_token",
                value='a9a206e9-53f3-4ce1-bba4-954cb575c953',
                domain="**************",
                path="/"
            )
            self.client.cookies.set(
                name="lName",
                value='YWRtaW4=',
                # domain="**************",
                # path="/dms-admin"
            )
            self.client.cookies.set(
                name="SESSION",
                value='MDMxM2M3NzUtZWMwNy00ODJhLTg5M2QtYTU0MDRkMzIzMDgw',
                # domain="**************",
                # path="/dms-sso/"
            )
        else:
            print("Failed to get token.")

    @task
    def index_url(self):
        # 目标地址 
        url = 'http://**************/dms/'
        if hasattr(self, 'headers'):
            # 发送带 token 的请求 
            response = self.client.get(url, cookies=self.client.cookies)
            if response.status_code != 200:
                print(f"Request failed with status code {response.status_code}")
            else:
                # 1. 解析CSS/JS资源（需安装BeautifulSoup）
                soup = BeautifulSoup(response.text, 'html.parser')
                resources = [link['href'] for link in soup.find_all('link', rel='stylesheet')] + \
                            [script['src'] for script in soup.find_all('script') if script.get('src')]
                # 2. 并发加载子资源
                for resource in resources:
                    self.client.get(resource, cookies=self.client.cookies)
                # 3. 加载API接口（根据curl中的Referer分析）
                self.client.get("/dms/rest/oauth/callBack?code=EPclWN",
                                headers={'Referer': self.headers['Referer']},
                                cookies=self.client.cookies)
        else:
            print("No valid token, skipping request.") 
