
import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Form';

/**
 * 表格列定义
 */
export const columns: BasicColumn[] = [
  {
    title: '小程序名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '小程序openid',
    dataIndex: 'openid',
    width: 180,
  },
  {
    title: '短描述',
    dataIndex: 'short_desc',
    width: 150,
  },
  {
    title: '发布状态',
    dataIndex: 'publish_status',
    width: 100,
    customRender: ({ record }) => {
      return record.publish_status ? '已发布' : '未发布';
    },
  },
  {
    title: '分类ID',
    dataIndex: 'class_id',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'create_datetime',
    width: 180,
  },
  {
    title: '小程序图标',
    dataIndex: 'icon',
    width: 180,
    customRender: ({ record }) => {
      if (record.icon) {
        return `<img src="${record.icon}" alt="图标" style={{ width: '30px', height: '30px', borderRadius: '4px' }} />`;
      }
      return '无图标';
    },
  },
];

/**
 * 搜索表单定义
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '小程序名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'openid',
    label: '小程序openid',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'publish_status',
    label: '发布状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '已发布', value: true },
        { label: '未发布', value: false },
      ],
    },
    colProps: { span: 6 },
  },
  {
    field: 'class_id',
    label: '分类ID',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 表单定义
 */
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '小程序名称',
    required: true,
    component: 'Input',
  },
  {
    field: 'openid',
    label: '小程序openid',
    required: true,
    component: 'Input',
  },
  {
    field: 'short_desc',
    label: '短描述',
    component: 'Input',
    required: false,
  },
  {
    field: 'desc',
    label: '详细描述',
    component: 'InputTextArea',
    required: false,
  },
  {
    field: 'publish_status',
    component: 'Switch',
    label: '发布状态',
    defaultValue: false,
    componentProps: {
      checkedChildren: '已发布',
      unCheckedChildren: '未发布',
    },
  },
  {
    field: 'icon',
    label: '小程序图标',
    component: 'Upload',
    required: false,
    componentProps: {
      maxSize: 2,
    },
  },
  {
    field: 'class_id',
    label: '分类ID',
    component: 'Input',
    required: false,
  },
];
