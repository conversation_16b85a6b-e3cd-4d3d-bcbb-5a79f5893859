from django.db import models

from fuadmin import settings
from utils.models import CoreModel


class Demo(CoreModel):

    name= models.CharField(help_text='项目名称', max_length=64, verbose_name='项目名称')
    code = models.CharField(help_text='项目编码', max_length=32, verbose_name='项目编码')
    status = models.CharField(help_text='项目状态', max_length=64, verbose_name='项目状态')
    test = models.CharField(help_text='test',null=True, blank=True, max_length=64, verbose_name='test')

class Meta:
    db_table = "Demo"
    verbose_name = '项目演示'
    verbose_name_plural = verbose_name
    ordering = ('-create_datetime',)
