<wxs src="../wxs/utils.wxs" module="utils" />

<van-popup
  show="{{ show }}"
  z-index="{{ zIndex }}"
  overlay="{{ overlay }}"
  transition="{{ transition }}"
  custom-class="van-dialog van-dialog--{{ theme }}{{ className }} custom-class"
  custom-style="width: {{ utils.addUnit(width) }};{{ customStyle }}"
  overlay-style="{{ overlayStyle }}"
  close-on-click-overlay="{{ closeOnClickOverlay }}"
  root-portal="{{ rootPortal }}"
  bind:close="onClickOverlay"
  bind:after-leave="onAfterLeave"
>
  <view
    wx:if="{{ title || useTitleSlot  }}"
    class="{{ utils.bem('dialog__header', { isolated: !(message || useSlot) }) }}"
  >
    <slot wx:if="{{ useTitleSlot }}" name="title" />
    <block wx:elif="{{ title }}">{{ title }}</block>
  </view>

  <slot wx:if="{{ useSlot }}" />
  <view
    wx:elif="{{ message }}"
    class="{{ utils.bem('dialog__message', [theme, messageAlign, { hasTitle: title }]) }}"
  >
    <text class="van-dialog__message-text">{{ message }}</text>
  </view>

  <van-goods-action wx:if="{{ theme === 'round-button' }}" custom-class="van-dialog__footer--round-button">
    <van-goods-action-button
      wx:if="{{ showCancelButton }}"
      size="large"
      loading="{{ loading.cancel }}"
      class="van-dialog__button van-hairline--right"
      custom-class="van-dialog__cancel cancle-button-class"
      custom-style="color: {{ cancelButtonColor }}"
      bind:click="onCancel"
    >
      {{ cancelButtonText }}
    </van-goods-action-button>
    <van-goods-action-button
      wx:if="{{ showConfirmButton }}"
      size="large"
      class="van-dialog__button"
      loading="{{ loading.confirm }}"
      custom-class="van-dialog__confirm confirm-button-class"
      custom-style="color: {{ confirmButtonColor }}"
      button-id="{{ confirmButtonId }}"
      open-type="{{ confirmButtonOpenType }}"
      lang="{{ lang }}"
      business-id="{{ businessId }}"
      session-from="{{ sessionFrom }}"
      send-message-title="{{ sendMessageTitle }}"
      send-message-path="{{ sendMessagePath }}"
      send-message-img="{{ sendMessageImg }}"
      show-message-card="{{ showMessageCard }}"
      app-parameter="{{ appParameter }}"
      bindagreeprivacyauthorization="onAgreePrivacyAuthorization"
      bindgetRealTimePhoneNumber="onGetRealTimePhoneNumber"
      bind:click="onConfirm"
      bindgetuserinfo="onGetUserInfo"
      bindcontact="onContact"
      bindgetphonenumber="onGetPhoneNumber"
      binderror="onError"
      bindlaunchapp="onLaunchApp"
      bindopensetting="onOpenSetting"
    >
      {{ confirmButtonText }}
    </van-goods-action-button>
  </van-goods-action>

  <view wx:elif="{{ showCancelButton || showConfirmButton }}" class="van-hairline--top van-dialog__footer">
    <block wx:if="{{ showCancelButton }}">
      <slot wx:if="{{ useCancelButtonSlot }}" name="cancel-button" />

      <van-button
        wx:else
        size="large"
        loading="{{ loading.cancel }}"
        class="van-dialog__button van-hairline--right"
        custom-class="van-dialog__cancel cancle-button-class"
        custom-style="color: {{ cancelButtonColor }}"
        bind:click="onCancel"
      >
        {{ cancelButtonText }}
      </van-button>
    </block>

    <block wx:if="{{ showConfirmButton }}">
      <slot wx:if="{{ useConfirmButtonSlot }}" name="confirm-button" />

      <van-button
        wx:else
        size="large"
        class="van-dialog__button"
        loading="{{ loading.confirm }}"
        custom-class="van-dialog__confirm confirm-button-class"
        custom-style="color: {{ confirmButtonColor }}"
        button-id="{{ confirmButtonId }}"
        open-type="{{ confirmButtonOpenType }}"
        lang="{{ lang }}"
        business-id="{{ businessId }}"
        session-from="{{ sessionFrom }}"
        send-message-title="{{ sendMessageTitle }}"
        send-message-path="{{ sendMessagePath }}"
        send-message-img="{{ sendMessageImg }}"
        show-message-card="{{ showMessageCard }}"
        app-parameter="{{ appParameter }}"
        bindagreeprivacyauthorization="onAgreePrivacyAuthorization"
        bindgetRealTimePhoneNumber="onGetRealTimePhoneNumber"
        bind:click="onConfirm"
        bindgetuserinfo="onGetUserInfo"
        bindcontact="onContact"
        bindgetphonenumber="onGetPhoneNumber"
        binderror="onError"
        bindlaunchapp="onLaunchApp"
        bindopensetting="onOpenSetting"
      >
        {{ confirmButtonText }}
      </van-button>
    </block>
  </view>
</van-popup>
