"""
ChromiumController - 基于DrissionPage 4.1+的浏览器控制工具
功能：
1. 原生Chromium浏览器实例管理
2. 原子级标签页操作
3. 完整的生命周期控制
4. 符合现代反爬对抗策略
"""
from fastapi import FastAPI, File, UploadFile
from fastapi.responses import JSONResponse
import tempfile
from docx import Document
from pycorrector import Corrector
import language_tool_python

from DrissionPage import Chromium, ChromiumOptions
from typing import Optional, Dict, List
import logging
import time

from DrissionPage._pages.chromium_tab import ChromiumTab


class ChromiumConfig:
    """浏览器配置工厂"""

    def __init__(
            self,
            headless: bool = True,
            proxy: Optional[str] = None,
            user_agent: Optional[str] = None,
            window_size: tuple = (1366, 768),
            timeout: float = 15.0,
            disable_images: bool = False,
            stealth_mode: bool = True,
            sandbox: bool = False,
            experimental_args: Optional[List[str]] = None
    ):
        self.headless = headless
        self.proxy = proxy
        self.user_agent = user_agent or (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/120.0.0.0 Safari/537.36"
        )
        self.window_size = window_size
        self.timeout = timeout
        self.disable_images = disable_images
        self.stealth_mode = stealth_mode
        self.sandbox = sandbox
        self.experimental_args = experimental_args or []


class ChromiumController:
    """Chromium浏览器核心控制器"""

    def __init__(self, _config: ChromiumConfig = ChromiumConfig()):
        self.config = _config
        self.browser: Optional[Chromium] = None
        self.active_tab: Optional[ChromiumTab] = None
        self._tabs: Dict[str, ChromiumTab] = {}
        self._init_browser()

    def _init_browser(self):
        """初始化浏览器引擎"""
        try:
            # 构建配置选项
            co = ChromiumOptions()

            # 基础配置
            co.set_argument('--window-size', f'{self.config.window_size[0]},{self.config.window_size[1]}')
            co.no_imgs(self.config.disable_images)
            co.incognito()  # 默认启用无痕模式

            # 高级配置
            if self.config.stealth_mode:
                co.set_argument('--disable-blink-features=AutomationControlled')
                co.remove_argument('excludeSwitches')
                co.remove_argument('useAutomationExtension')

            if self.config.proxy:
                co.set_proxy(self.config.proxy)

            if not self.config.sandbox:
                co.set_argument('--no-sandbox')

            # 实验性参数
            for arg in self.config.experimental_args:
                co.set_argument(arg)

            # 创建浏览器实例
            self.browser = Chromium(co)

            # 初始标签页管理
            self._sync_tabs()
            logging.info("Chromium initialized with PID: %d", self.browser.process_id)

        except Exception as e:
            logging.error("Browser initialization failed: %s", str(e))
            self.cleanup()
            raise

    def _sync_tabs(self,active_tab_id:str=None):
        """同步标签页状态"""
        self._tabs = {tab.tab_id: tab for tab in self.browser.get_tabs()}
        if active_tab_id:
            self.browser.activate_tab(active_tab_id)
            self.active_tab = active_tab_id

    def create_tab(self, url: Optional[str] = None) -> str:
        """创建新标签页（原子操作）"""
        try:
            new_tab = self.browser.new_tab(url=url)
            self._sync_tabs()
            logging.info("Created new tab [%s] with URL: %s", new_tab.tab_id, url)
            return new_tab.tab_id
        except Exception as e:
            logging.error("Tab creation failed: %s", str(e))
            raise

    def navigate(self, url: str, tab_id: Optional[str] = None):
        """导航到指定URL（支持跨标签）"""
        target_tab = self._get_tab(tab_id)
        try:
            target_tab.get(url)
            target_tab.wait.load_start(timeout=self.config.timeout)
            logging.info("Navigated tab [%s] to: %s", target_tab.tab_id, url)
        except Exception as e:
            logging.error("Navigation failed: %s", str(e))
            self._capture_debug_screenshot(target_tab)
            raise

    def switch_tab(self, tab_id: str):
        """切换活动标签页"""
        if tab_id not in self._tabs:
            raise ValueError(f"Invalid tab ID: {tab_id}")
        try:
            self.browser.activate_tab(tab_id)
            self._sync_tabs()
            logging.debug("Switched to tab [%s]", tab_id)
        except Exception as e:
            logging.error("Tab switching failed: %s", str(e))
            raise

    def close_tab(self, tab_id: str):
        """关闭指定标签页"""
        if len(self._tabs) <= 1:
            logging.warning("Cannot close the last remaining tab")
            return
        try:
            self.browser.close_tabs(tabs_or_ids=tab_id)
            self._sync_tabs()
            logging.info("Closed tab [%s]", tab_id)
        except Exception as e:
            logging.error("Tab closure failed: %s", str(e))
            raise

    def execute_script(self, script: str, tab_id: Optional[str] = None, *args):
        """执行跨标签JavaScript"""
        target_tab = self._get_tab(tab_id)
        try:
            result = target_tab.run_js(script, *args)
            logging.debug("Executed script in tab [%s]", target_tab.tab_id)
            return result
        except Exception as e:
            logging.error("Script execution failed: %s", str(e))
            return None

    def wait_for_element(self, selector: str, tab_id: Optional[str] = None, timeout: Optional[float] = None):
        """智能元素等待"""
        target_tab = self._get_tab(tab_id)
        timeout = timeout or self.config.timeout
        try:
            element = target_tab.wait.eles_loaded(selector, timeout=timeout)
            return element
        except Exception as e:
            logging.error("Element wait failed: %s", str(e))
            self._capture_debug_screenshot(target_tab)
            raise

    def _get_tab(self, tab_id: Optional[str] = None) -> ChromiumTab:
        """安全获取标签页对象"""
        if tab_id:
            if tab_id not in self._tabs:
                raise ValueError(f"Tab ID {tab_id} not found")
            return self._tabs[tab_id]
        return self.active_tab

    def _capture_debug_screenshot(self, tab: ChromiumTab, prefix: str = "debug"):
        """调试截图"""
        print("当前激活的标签" + str(self.active_tab.tab_id))
        timestamp = int(time.time())
        filename = f"{prefix}_{tab.tab_id}_{timestamp}.png"
        try:
            tab.get_screenshot(name=filename, full_page=True)
            logging.warning("Saved debug screenshot: %s", filename)
        except Exception as e:
            logging.error("Failed to capture screenshot: %s", str(e))

    def cleanup(self):
        """资源清理"""
        if self.browser:
            try:
                self.browser.quit()
                logging.info("Browser process terminated")
            except Exception as e:
                logging.error("Browser termination failed: %s", str(e))
        self._tabs.clear()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()


# 使用示例
if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)

    # 高级配置示例
    config = ChromiumConfig(
        headless=False,
        stealth_mode=False,
        experimental_args=[
            "--disable-web-security",
            "--disable-popup-blocking"
        ],
        disable_images=False,
        proxy=None,
        user_agent=None,
        window_size=(1920, 1080),
        timeout=15.0
    )

    with ChromiumController(config) as ctrl:
        # 创建初始标签页
        main_tab = ctrl.create_tab("https://www.baidu.com")
        ctrl.wait_for_element("#kw", main_tab)
        print(f"Main page title: {ctrl.active_tab}")

        # 创建数据采集标签页
        data_tab = ctrl.create_tab()
        ctrl.navigate("https://www.baidu.com", data_tab)
        ctrl.wait_for_element("#kw", data_tab)

        # 并行操作
        ctrl.switch_tab(main_tab)
        ctrl.execute_script("document.querySelector('a.more').click()")

        # 采集数据
        ctrl.switch_tab(data_tab)
        dataset = ctrl.execute_script("""
            return Array.from(document.querySelectorAll('#dataset-table tr'))
                .map(row => {
                    const cells = row.querySelectorAll('td');
                    return {
                        id: cells[0].innerText,
                        value: cells[1].innerText
                    };
                });
        """)
        print(f"Collected {dataset}")

        # 关闭示例标签页
        ctrl.close_tab(main_tab)