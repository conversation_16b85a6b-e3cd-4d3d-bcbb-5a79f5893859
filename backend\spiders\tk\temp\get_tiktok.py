import logging
import random

from DrissionPage import ChromiumPage,ChromiumOptions
from DrissionPage.errors import ElementLostError

import time
from celery import shared_task

from django.core.exceptions import ValidationError

from spiders.models import TiktokLink
from utils.StringUtils import StringUtils

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TiktokDownloader:
    def __init__(self):
        logging.info('init...')
        self.co = ChromiumOptions()
        self._configure_browser()
        self.page = ChromiumPage(self.co)
        self.page.set.retry_times(3)  # 设置重试次数
        self.max_retries = 10 # 重试次数
        self.retry_count = 0 # 重试计数器
        self.page.set.timeouts(200)  # 设置超时时间
    def _configure_browser(self):
        """配置浏览器参数"""
        (self.co.headless(True)
          .set_argument('--window-size', '1920,1080')
         .mute(True).
         set_pref('download.prompt_for_download', False)
         .set_local_port(random.randint(1000, 9999))
         .set_pref(arg='profile.default_content_settings.popups', value='0')
         )

    def get_videos_url(self):
        logging.info('get_videos_url')
        """获取 TikTok 热门视频"""
        self.page.get('https://www.tiktok.com/explore')
        time.sleep(10)
        # 获取视频连接
        video_cards = self.page.eles('xpath://body//div[@id="main-content-explore_page"]/div/div[2]/div/div/div[1]//a')
        _links = []
        # 逐个获取视频信息
        for link in video_cards:
            _tmp_link = link.attr('href')
            _links.append(_tmp_link)
        for link in _links:
            self.get_link_details(link)

    def get_link_details(self, link):
        logging.info('get_videos_url')
        _title = ''
        _auth = ''
        self.retry_count = 0
        # 重试十次
        while self.retry_count < self.max_retries:
            try:
                self.page.get(link)
                time.sleep(10)
                self.page.scroll.down(500*self.retry_count)
                # 尝试获取元素
                _title = self.page.ele('xpath://h1[@data-e2e="browse-video-desc"]').text
                _auth = self.page.ele('xpath://span[@data-e2e="browse-username"]').text
                _url = self.page.url
                _likes = self.page.ele('xpath://strong[@data-e2e="like-count"]').text
                _comments = self.page.ele('xpath://strong[@data-e2e="comment-count"]').text
                _collects = self.page.ele('xpath://strong[@data-e2e="undefined-count"]').text
                _shares = self.page.ele('xpath://strong[@data-e2e="share-count"]').text
                # 转换成数字
                stringUtils = StringUtils()
                _likes = stringUtils.convert_abbreviated_number(_likes)
                _comments = stringUtils.convert_abbreviated_number(_comments)
                _collects = stringUtils.convert_abbreviated_number(_collects)
                _shares = stringUtils.convert_abbreviated_number(_shares)

                # 前置验证
                if not _url.startswith(('http://', 'https://')):
                    raise ValidationError("URL 格式错误")

                # 检查是否存在
                exists = TiktokLink.objects.filter(link=_url).exists()
                if exists:
                    print(f"[WARN] 链接 {_url} 已存在")
                    return False
                TiktokLink.objects.create(
                    title=_title,
                    auther=_auth,
                    link=_url,
                    likes=_likes,
                    comments=_comments,
                    collects=_collects,
                    shares=_shares,
                )
                break  # 成功则跳出循环
            except ElementLostError as e:
                logging.info(f"第 {self.retry_count + 1} 次尝试失败: {e}")
                print(f"第 {self.retry_count + 1} 次尝试失败: {e}")
                self.retry_count += 1
                if self.retry_count >= self.max_retries:
                    print("已达到最大重试次数（10次），退出循环")
                    logging.info("已达到最大重试次数（10次），退出循环")
                    break
                time.sleep(2)  # 间隔 2 秒重试
            except Exception as e:
                print(f"其他异常: {e}")
                logging.info(self.page.url)
                logging.info(f"其他异常: {e}")
                break  # 非预期异常直接退出
            finally:
                self.page.clear_cache()

@shared_task
def get_tiktok_task():
    tiktokDownloader = TiktokDownloader()
    tiktokDownloader.get_videos_url()


