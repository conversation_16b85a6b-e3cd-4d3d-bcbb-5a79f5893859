"""
下载方法类
"""
import logging
import random
import time
from pathlib import Path

from DrissionPage import ChromiumOptions,Chromium
from DrissionPage.errors import ElementLostError, ContextLostError

from utils.FileFolderUtils import FileFolderUtils
from utils.StringUtils import StringUtils

logger = logging.getLogger(__name__)
class DownloadTK:
    def __init__(self):
        self.download_dir = Path(r'E:\\datas\\files\\tiktok')
        self.max_retries = 5
        self.co = ChromiumOptions()
        self._configure_browser() # 浏览器配置
        self.chrome = Chromium(addr_or_opts=self.co) # 创建一个浏览器
        self._config_page() # 页面相关配置
        self.tabs = [] # 保存打开得tab
        # self._prepare_download_directory()

    def _configure_browser(self):
        """配置浏览器参数"""
        self.co.set_download_path(str(self.download_dir))  # 设置下载路径:cite[1]
        self.co.headless(True)
        self.co.mute(True)
        # self.co.set_user_data_path(self.download_dir.joinpath('temp'))
        self.co.set_argument('--window-size', '1920,1080')
        self.co.set_argument('--start-maximized')# 设置启动时最大化
        self.co.set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
        self.co.set_local_port(random.randint(1000, 9999))
        self.co.set_pref(arg='profile.default_content_settings.popups', value='0')

    def _config_page(self):
        """配置page参数"""
        self.chrome.set.retry_times(3)
        self.chrome.set.timeouts(20)
        self.chrome.set.download_path(str(self.download_dir))
        # self.page.set.download_file_name('temp.mp4')

    def _prepare_download_directory(self):
        """准备下载目录"""
        self.download_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f'下载目录准备就绪: {self.download_dir}')

    def _input_url(self,current_tab, url: str):
        """输入目标URL"""
        logger.info(f"当前执行环境 chrome:{self.chrome} tab: {current_tab}")
        try:
            input_selector = '#url'  # 改用CSS选择器
            current_tab.wait.eles_loaded(input_selector, timeout=10)
            url_input = current_tab.ele(input_selector)
            url_input.input(clear=True, vals=url)
            logger.info('成功输入URL')
        except ElementLostError as e:
            logger.error('URL输入框定位失败')
            raise RuntimeError('页面元素加载异常') from e

    def _click_submit_button(self,current_tab):
        """点击提交按钮"""
        logger.info(f"当前执行环境 chrome:{self.chrome} tab: {current_tab}")
        try:
            btn_selector = '@@tag()=button@@type=submit'
            current_tab.wait.eles_loaded(btn_selector, timeout=10)
            submit_btn = current_tab.ele(btn_selector)
            submit_btn.click()
            logger.info('已点击提交按钮')
        except ElementLostError as e:
            logger.error('提交按钮定位失败')
            raise RuntimeError('无法提交表单') from e

    def _handle_download_button(self,current_tab):
        logger.info(f"当前执行环境 chrome:{self.chrome} tab: {current_tab}")
        """处理下载按钮"""
        try:
            download_btn_selector = '@@tag()=a@@class=flex items-center justify-center w-full gap-2 p-2 mt-2 text-center text-white rounded bg-primary'  # 通过类名定位
            current_tab.wait.eles_loaded(download_btn_selector, timeout=15)
            download_btn = current_tab.ele(download_btn_selector)
            mission = download_btn.click.to_download()
            logger.info('已触发下载操作')
            mission.wait()
            if mission:
                logger.info(mission.is_done)
                # mission.wait()
                logger.info(f'文件下载完成: {mission}')
                return True
            else:
                logger.error(f'文件下载失败: {mission}')
                return False


        except (ElementLostError, ContextLostError) as e:
            logger.error('下载按钮操作失败')
            raise RuntimeError('下载流程中断') from e

    def download(self, url: str):
        """判断"""
        target_path = Path.joinpath(self.download_dir).joinpath('SaveTik_Net_' + StringUtils.split_last_separator(url, '/') + '.mp4')
        logger.info(f'判断文件是否存在:{target_path}')
        if FileFolderUtils.file_exists(target_path):
            logger.error('文件存在')
            return False

        """执行下载流程"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f'开始下载任务 (尝试 {attempt + 1}/{self.max_retries})')
                current_tab = self.chrome.new_tab()
                self.tabs.append(current_tab)
                current_tab.get('https://savetik.net/', retry=3)
                self._input_url(current_tab,url)
                self._click_submit_button(current_tab)
                time.sleep(5)  # 需要适当固定等待配合动态等待
                if self._handle_download_button(current_tab):
                    return True
            except RuntimeError as e:
                logger.warning(f'流程中断: {str(e)}')
                # current_tab.get('https://savetik.net/', retry=3)
            except Exception as e:
                logger.error(f'未知错误: {str(e)}')
                # self..get('about:blank')  # 重置页面状态
            finally:
                # 清除缓存并关闭浏览器
                self.chrome.clear_cache()
                self.chrome.quit()


        logger.error(f'经过{self.max_retries}次尝试仍失败')
        return False

    def cleanup(self):
        """清理浏览器资源"""
        self.page.quit()
        logger.info("浏览器实例已关闭")

