.ant-checkbox-group-horizontal .ant-checkbox-group-body {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.ant-checkbox-group-horizontal .ant-checkbox-group-body .ant-list-item-line {
  padding-right: 0;
}
.ant-checkbox-group-horizontal .ant-checkbox-group-body .ant-checkbox-item {
  flex-flow: 0;
}
.ant-checkbox-group-horizontal .ant-checkbox-group-body .ant-list-item-line::after {
  display: none;
}
.ant-checkbox-group-header:empty,
.ant-checkbox-group-footer:empty {
  display: none;
}
.ant-checkbox-group-header,
.ant-checkbox-group-footer {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  line-height: 1.4;
  font-size: 30rpx;
  color: var(--checkbox-header-color, #999999);
}
.ant-checkbox-group-body {
  position: relative;
  overflow: hidden;
}
.ant-checkbox-group-body .ant-checkbox-item-content .ant-checkbox-group-item-label-default:not(:nth-child(1)) {
  display: none;
}
