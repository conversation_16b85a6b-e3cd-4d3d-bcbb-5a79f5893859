#!/usr/bin/env python
# -*- coding: utf-8 -*-
# time: 2025/8/4
# file: tasks.py
# author: AI Assistant
# 爬虫任务定义

import logging
from celery import shared_task

from spiders.douyin.hot_search.hot_search import TiktokHotSearchSpider

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@shared_task
def crawl_tiktok_hot_search():
    """爬取抖音热搜数据的Celery任务"""
    logger.info('开始执行抖音热搜爬虫任务')
    spider = TiktokHotSearchSpider()
    try:
        result = spider.crawl()
        if result:
            logger.info('抖音热搜爬虫任务执行成功')
        else:
            logger.warning('抖音热搜爬虫任务未获取到数据')
        return result
    except Exception as e:
        logger.error(f'抖音热搜爬虫任务执行失败: {str(e)}')
        return False
    finally:
        spider.cleanup()