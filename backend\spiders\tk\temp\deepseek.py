# import logging
# import random
# import time
# from pathlib import Path
# from DrissionPage import Chromium,ChromiumPage,ChromiumOptions
# from DrissionPage.errors import ElementLostError, ContextLostError
#
# from utils.FileFolderUtils import FileFolderUtils
# from utils.StringUtils import StringUtils
# from django.db import transaction
# from django.db.models import Q
# from spiders.models import TiktokLink
# import os
#
# # 配置日志
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('tiktok_downloader.log'),
#         logging.StreamHandler()
#     ]
# )
# logger = logging.getLogger(__name__)
#
# class TiktokDBManager:
#     @staticmethod
#     def get_pending_links(limit=10):
#         """获取待处理的下载链接"""
#         with transaction.atomic():
#             # 使用select_for_update锁定记录，防止并发冲突
#             queryset = TiktokLink.objects.select_for_update(skip_locked=True,nowait=False).filter(
#                 Q( download_status='0') | Q(download_status='2')| Q(download_status='4')
#             ).order_by('create_datetime')[:10]
#
#             return list(queryset)
#
#     @staticmethod
#     def update_download_status(link_obj, status):
#         """更新下载状态"""
#         try:
#             with transaction.atomic():
#                 link_obj.download_status = status
#                 link_obj.save(update_fields=['download_status', 'update_datetime'])
#         except Exception as e:
#             logger.error(f'更新状态失败{e}')
#
#
# # 修改后的下载管理类
# class TiktokDownloadManager:
#     def __init__(self):
#         self.downloader = DownloadTK()
#         self.max_retries = 3
#
#     def process_downloads(self, batch_size=5):
#         """处理批量下载"""
#         links = TiktokDBManager.get_pending_links(batch_size)
#         if not links:
#             logger.info("没有待处理的下载任务")
#             return
#
#         for link in links:
#             logger.info(link )
#             self._process_single_link(link)
#
#     def _process_single_link(self, link):
#         """处理单个下载任务"""
#
#         try:
#             # 生成唯一文件名
#
#             # file_name = f"{link.author}_{link.id}.mp4"
#             logger.info('开始判断文件是否存在1')
#             logger.info(self.downloader.download_dir)
#             logger.info('开始判断文件是否存在2')
#             logger.info(link.link)
#             logger.info('开始判断文件是否存在3')
#             target_path= Path.joinpath(self.downloader.download_dir).joinpath('SaveTik_Net_'+StringUtils.split_last_separator(link.link,'/')+'.mp4')
#             logger.info(f'目标路径:{str(target_path)}')
#             # 检查是否已下载
#             if os.path.exists(target_path):
#                 logger.info(f'目标路径存在:{str(target_path)}')
#                 self._handle_existing_file(link, target_path)
#                 logger.info(f'_process_single_link返回')
#                 return
#             # 执行下载
#             success = self.downloader.download(link.link)
#
#             if success:
#                 self._handle_success(link, target_path)
#             else:
#                 self._handle_failure(link)
#
#         except Exception as e:
#             logger.error(f"下载异常: {str(e)}")
#             self._update_failure_status(link, str(e))
#
#     def _handle_existing_file(self, link, path):
#         """处理已存在文件"""
#         print(self.max_retries)
#         logger.info(f"文件已存在: {path}")
#         logger.info(f"更新下载状态")
#         TiktokDBManager.update_download_status(
#             link,
#             status=1
#         )
#
#     def _handle_success(self, link, path):
#         """处理成功下载"""
#         print(self.max_retries)
#         logger.info(f"下载成功: {path}")
#         TiktokDBManager.update_download_status(
#             link,
#             status=1
#         )
#
#     def _handle_failure(self, link):
#         """处理下载失败"""
#         logger.warning(f"下载失败: {link.link}")
#         new_retries = link.retries + 1
#         status = 2 if new_retries >= self.max_retries else 3
#
#         TiktokDBManager.update_download_status(
#             link,
#             status=status
#         )
#
#     def _update_failure_status(self, link, error_msg):
#         """更新异常状态"""
#         print(self.max_retries)
#         logger.error(f"更新数据库状态失败: {error_msg}")
#         TiktokDBManager.update_download_status(
#             link,
#             status=4
#         )
#
#
# # 在DownloadTK类中添加以下方法
# class DownloadTK:
#     def __init__(self):
#         self.download_dir = Path(r'E:\\datas\\files\\tiktok')
#         self.max_retries = 5
#         self.co = ChromiumOptions()
#         self._configure_browser()
#         self.page = ChromiumPage(addr_or_opts=self.co)
#         self._config_page()
#         self.tab = ''
#         # self._prepare_download_directory()
#
#     def _configure_browser(self):
#         """配置浏览器参数"""
#         self.co.set_download_path(str(self.download_dir))  # 设置下载路径:cite[1]
#         self.co.set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
#         self.co.set_argument('--kiosk-printing')  # 静默模式（可选）:cite[3]
#         self.co.incognito()  # 隐身模式
#         self.co.set_paths(local_port=random.randint(1000, 9999))
#         self.co.set_argument(f'--user-data-dir=./temp/profile_{random.randint(1000, 9999)}')
#         self.co.set_argument('--disable-blink-features=AutomationControlled')
#         self.co.set_argument('--no-sandbox')
#         self.co.set_argument('--disable-dev-shm-usage')
#         self.co.set_argument('--remote-debugging-port=' + str(random.randint(9200, 9300)))
#
#     def _config_page(self):
#         """配置page参数"""
#         self.page.set.retry_times(3)
#         self.page.set.timeouts(20)
#         self.page.set.download_path(str(self.download_dir))
#         # self.page.set.download_file_name('temp.mp4')
#
#     def _prepare_download_directory(self):
#         """准备下载目录"""
#         self.download_dir.mkdir(parents=True, exist_ok=True)
#         logger.info(f'下载目录准备就绪: {self.download_dir}')
#
#     def _input_url(self, url: str):
#         """输入目标URL"""
#         try:
#             input_selector = '#url'  # 改用CSS选择器
#             self.page.wait.eles_loaded(input_selector, timeout=10)
#             url_input = self.page.ele(input_selector)
#             url_input.input(clear=True, vals=url)
#             logger.info('成功输入URL')
#         except ElementLostError as e:
#             logger.error('URL输入框定位失败')
#             raise RuntimeError('页面元素加载异常') from e
#
#     def _click_submit_button(self):
#         """点击提交按钮"""
#         try:
#             btn_selector = '@@tag()=button@@type=submit'
#             self.page.wait.eles_loaded(btn_selector, timeout=10)
#             submit_btn = self.page.ele(btn_selector)
#             submit_btn.click()
#             logger.info('已点击提交按钮')
#         except ElementLostError as e:
#             logger.error('提交按钮定位失败')
#             raise RuntimeError('无法提交表单') from e
#
#     def _handle_download_button(self):
#         """处理下载按钮"""
#         try:
#             download_btn_selector = '@@tag()=a@@class=flex items-center justify-center w-full gap-2 p-2 mt-2 text-center text-white rounded bg-primary'  # 通过类名定位
#             self.page.wait.eles_loaded(download_btn_selector, timeout=15)
#             download_btn = self.page.ele(download_btn_selector)
#             mission = download_btn.click.to_download()
#             logger.info('已触发下载操作')
#             mission.wait()
#             if mission:
#                 mission.wait()
#                 logger.info(f'文件下载完成: {mission}')
#                 return True
#             else:
#                 logger.error(f'文件下载失败: {mission}')
#                 return False
#
#
#         except (ElementLostError, ContextLostError) as e:
#             logger.error('下载按钮操作失败')
#             raise RuntimeError('下载流程中断') from e
#
#     def download(self, url: str):
#         """判断"""
#         target_path = Path.joinpath(self.download_dir).joinpath('SaveTik_Net_' + StringUtils.split_last_separator(url, '/') + '.mp4')
#         logger.info(f'判断文件是否存在:{target_path}')
#         if FileFolderUtils.file_exists(target_path):
#             logger.error('文件存在')
#             return False
#
#         """执行下载流程"""
#         for attempt in range(self.max_retries):
#             try:
#                 logger.info(f'开始下载任务 (尝试 {attempt + 1}/{self.max_retries})')
#                 self.tab = self.page.get('https://savetik.net/', retry=3)
#
#                 self._input_url(url)
#                 self._click_submit_button()
#                 time.sleep(5)  # 需要适当固定等待配合动态等待
#                 if self._handle_download_button():
#                     return True
#
#             except RuntimeError as e:
#                 logger.warning(f'流程中断: {str(e)}')
#                 self.page.refresh()
#             except Exception as e:
#                 logger.error(f'未知错误: {str(e)}')
#                 self.page.get('about:blank')  # 重置页面状态
#             finally:
#                 self.page.close()
#
#         logger.error(f'经过{self.max_retries}次尝试仍失败')
#         return False
#
#     def cleanup(self):
#         """清理浏览器资源"""
#         self.page.quit()
#         logger.info("浏览器实例已关闭")
#
#
#
