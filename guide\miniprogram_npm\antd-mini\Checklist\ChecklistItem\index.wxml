<view
  class="ant-checklist-item"
  bindtap="{{item.disabled || item.readonly ? '' : 'onChecklistItemClick'}}"
  hoverClass="{{item.disabled || item.readonly ? '' : 'ant-checklist-item-hover'}}"
  hoverStartTime="{{20}}"
  hoverStayTime="{{40}}"
>
  <view class="ant-checklist-item-content {{item.disabled ? 'ant-checklist-item-content-disabled' : ''}}">
    <view class="ant-checklist-item-content-box">
      <slot
        name="content"
        item="{{item}}"
      ></slot>
    </view>
    <view
      wx:if="{{checked}}"
      class="ant-checklist-item-content-icon"
    >
      <slot name="icon"></slot>
    </view>
  </view>
  <view class="ant-checklist-item-line"></view>
</view>