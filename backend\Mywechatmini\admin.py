from django.contrib import admin
from Mywechatmini.models import MyWechat


@admin.register(MyWechat)
class MyWechatAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'openid', 'short_desc', 'publish_status', 'class_id', 'create_datetime')
    search_fields = ('name', 'openid', 'class_id')
    list_filter = ('publish_status', 'class_id')
    ordering = ('-create_datetime',)
    list_per_page = 10

    fieldsets = (
        ('基本信息', {'fields': ('name', 'openid', 'short_desc', 'desc')}),
        ('分类与状态', {'fields': ('class_id', 'publish_status')}),
        ('图标', {'fields': ('icon',)}),
    )