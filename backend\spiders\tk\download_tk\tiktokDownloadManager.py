
import logging
import os
from pathlib import Path

from spiders.common.save_tk.save_tk_website import SaveTkWebsite
from spiders.tk.download_tk.downloadTk import DownloadTK
from spiders.tk.download_tk.tiktokDBManager import TiktokDBManager
from utils.StringUtils import StringUtils

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tiktok_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
# 修改后的下载管理类
class TiktokDownloadManager:
    def __init__(self):
        self.downloader = DownloadTK()
        self.max_retries = 3

    def process_downloads(self, batch_size=1):
        """处理批量下载"""
        links = TiktokDBManager.get_pending_links(batch_size)
        if not links:
            logger.info("没有待处理的下载任务")
            return

        for link in links:
            logger.info(link )
            self._process_single_link(link)

    def _process_single_link(self, link):
        """处理单个下载任务"""
        try:
            # 生成唯一文件名
            target_path= Path.joinpath(self.downloader.download_dir).joinpath('SaveTik_Net_'+StringUtils.split_last_separator(link.link,'/')+'.mp4')
            logger.info(f'目标路径:{str(target_path)}')
            # 检查是否已下载
            if os.path.exists(target_path):
                logger.info(f'目标路径存在:{str(target_path)}')
                self._handle_existing_file(link, target_path)
                logger.info(f'_process_single_link返回')
                return
            # 执行下载
            saveTkWebsite = SaveTkWebsite(link.link)
            saveTkWebsite.download_line()
        except Exception as e:
            logger.error(f"下载异常: {str(e)}")
            self._update_failure_status(link, str(e))


    def _handle_existing_file(self, link, path):
        """处理已存在文件"""
        print(self.max_retries)
        logger.info(f"文件已存在: {path}")
        logger.info(f"更新下载状态")
        TiktokDBManager.update_download_status(
            link,
            status=1
        )

    def _handle_success(self, link, path):
        """处理成功下载"""
        print(self.max_retries)
        logger.info(f"下载成功: {path}")
        TiktokDBManager.update_download_status(
            link,
            status=1
        )

    def _handle_failure(self, link):
        """处理下载失败"""
        logger.warning(f"下载失败: {link.link}")
        print(self.max_retries)
        TiktokDBManager.update_download_status(
            link,
            status=2
        )

    def _update_failure_status(self, link, error_msg):
        """更新异常状态"""
        print(self.max_retries)
        logger.error(f"更新数据库状态失败: {error_msg}")
        TiktokDBManager.update_download_status(
            link,
            status=4
        )
