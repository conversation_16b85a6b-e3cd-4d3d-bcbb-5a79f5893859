import re
import random
import string
from typing import List, Union

class StringUtils:
    def __init__(self):
        self.version = 1.0
    def convert_abbreviated_number(self,s):
        print(self.version)
        """
        将抖音风格的缩写数字转换为实际数值
        示例:
        "5.2K" → 5200
        "3M" → 3_000_000
        "500" → 500
        """
        # 定义单位映射表（可扩展）
        units = {
            'K': 1_000,
            'M': 1_000_000,
            'B': 1_000_000_000,
            '万': 10_000,  # 中文万
            '亿': 100_000_000  # 中文亿
        }

        # 使用正则匹配数字和单位
        match = re.match(r"^([\d.,]+)\s*([KMB万亿]?)$", str(s).upper())
        if not match:
            raise ValueError(f"无效的数字格式: {s}")

        number_part, unit = match.groups()

        # 移除数字中的逗号（适配不同格式）
        number = float(number_part.replace(',', ''))

        # 计算实际数值
        multiplier = units.get(unit, 1)  # 默认乘数为1
        return int(number * multiplier) if unit else number


    @staticmethod
    def reverse_string(s: str) -> str:
        """反转字符串"""
        return s[::-1]

    @staticmethod
    def count_substring(s: str, sub: str) -> int:
        """统计子字符串出现次数（区分大小写）"""
        return s.count(sub) if s and sub else 0

    @staticmethod
    def is_palindrome(s: str) -> bool:
        """检查是否为回文字符串（忽略空格、大小写和标点）"""
        cleaned = re.sub(r'[^a-zA-Z0-9]', '', s).lower()
        return cleaned == cleaned[::-1]

    @staticmethod
    def to_camel_case(s: str, delimiter: str = '_') -> str:
        """下划线/连字符转驼峰命名"""
        parts = s.split(delimiter)
        return parts[0] + ''.join(word.capitalize() for word in parts[1:])

    @staticmethod
    def to_snake_case(s: str) -> str:
        """驼峰转下划线命名"""
        return re.sub(r'(?<!^)(?=[A-Z])', '_', s).lower()

    @staticmethod
    def remove_whitespace(s: str, keep_space: bool = False) -> str:
        """去除多余空白字符"""
        if keep_space:
            return ' '.join(s.split())
        return ''.join(s.split())

    @staticmethod
    def truncate_string(s: str, length: int, suffix: str = '...') -> str:
        """截断字符串并添加后缀"""
        return s[:length] + suffix if len(s) > length else s

    @staticmethod
    def contains_only(s: str, charset: str = string.ascii_letters + string.digits) -> bool:
        """检查字符串是否仅包含指定字符集"""
        return all(c in charset for c in s)

    @staticmethod
    def random_string(length: int = 8,
                      charset: str = string.ascii_letters + string.digits) -> str:
        """生成随机字符串"""
        return ''.join(random.choice(charset) for _ in range(length))

    @staticmethod
    def split_by_multiple(s: str, delimiters: List[str]) -> List[str]:
        """使用多个分隔符拆分字符串"""
        pattern = '|'.join(map(re.escape, delimiters))
        return re.split(pattern, s)

    @staticmethod
    def format_string(template: str, **kwargs) -> str:
        """安全格式化字符串（防止KeyError）"""

        class SafeDict(dict):
            def __missing__(self, key):
                return '{' + key + '}'

        return template.format_map(SafeDict(**kwargs))

    @staticmethod
    def levenshtein_distance(s1: str, s2: str) -> int:
        """计算两个字符串的编辑距离（Levenshtein distance）"""
        if len(s1) < len(s2):
            return StringUtils.levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = range(len(s2) + 1)
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    @staticmethod
    def split_last_slash(s: str) -> tuple:
        """
        截取最后一个'/'后的子串并返回剩余部分
        返回格式：(剩余部分, 截取部分)

        """
        # 查找最后一个斜杠的位置
        last_slash_index = s.rfind('/')

        # 处理不同情况
        if last_slash_index == -1:
            # 没有斜杠的情况
            return s, ''
        elif last_slash_index == len(s) - 1:
            # 斜杠在末尾的情况
            return s, ''
        else:
            # 正常截取情况
            remaining = s[:last_slash_index + 1]
            extracted = s[last_slash_index + 1:]
            return remaining, extracted

    @staticmethod
    # 扩展版本（支持自定义分隔符）
    def split_last_separator(s: str, sep: str = '/') -> string:
        """
        通用版本：截取最后一个分隔符后的子串
        参数：
            s - 原始字符串
            sep - 分隔符，默认'/'


        """
        last_sep_index = s.rfind(sep)
        if last_sep_index == -1:
            return ''
        return s[last_sep_index + len(sep):]



# 使用示例
if __name__ == '__main__':
    print("反转字符串:", StringUtils.reverse_string("hello world"))  # dlrow olleh
    print("统计子串:", StringUtils.count_substring("ababa", "aba"))  # 2
    print("回文检查:", StringUtils.is_palindrome("A man, a plan, a canal: Panama"))  # True
    print("驼峰转换:", StringUtils.to_camel_case("hello_world"))  # helloWorld
    print("下划线转换:", StringUtils.to_snake_case("HelloWorld"))  # hello_world
    print("去除空白:", StringUtils.remove_whitespace("  Hello \t World\n "))  # HelloWorld
    print("安全格式化:", StringUtils.format_string("{name} is {age}", age=20))  # {name} is 20
    print("编辑距离:", StringUtils.levenshtein_distance("kitten", "sitting"))  # 3
    print("随机字符串:", StringUtils.random_string(10))
    print('截取字符串',StringUtils.split_last_separator('https://sfsd/1231','2'))
