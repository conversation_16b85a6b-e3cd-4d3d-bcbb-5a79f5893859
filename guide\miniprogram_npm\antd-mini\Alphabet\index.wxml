<view class="alphabet-container">
  <view
    class="alphabet-container-item {{item === touchKey ? 'alphabet-container-active' : ''}}"
    catchtouchstart="onTouchStart"
    catchtouchmove="onTouchMove"
    catchtouchend="onTouchEnd"
    data-item="{{({ item, index })}}"
    id="{{'mya' + index}}"
    wx:for="{{alphabet}}"
    wx:for-index="index"
    wx:for-item="item"
    wx:key="*this"
  >{{item}}<view
      wx:if="{{item === touchKey}}"
      class="alphabet-container-item-tip"
    >{{item}}</view>
    <view
      wx:if="{{item === touchKey}}"
      class="alphabet-container-item-triangle"
    ></view>
  </view>
</view>