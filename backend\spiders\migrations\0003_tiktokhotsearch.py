# Generated by Django 5.2.3 on 2025-08-04 01:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('spiders', '0002_tiktoklink_download_status_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TiktokHotSearch',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('hot_index', models.PositiveIntegerField(help_text='热搜排序')),
                ('hot_title', models.CharField(db_index=True, default='', help_text='热搜标题', max_length=200, verbose_name='热搜标题')),
                ('hot_desc', models.TextField(default='', help_text='热搜描述', verbose_name='热搜描述')),
                ('hot_url', models.CharField(default='', help_text='热搜链接', max_length=500, verbose_name='热搜链接')),
                ('hot_type', models.CharField(default='', help_text='热搜类型', max_length=50, verbose_name='热搜类型')),
                ('hot_value', models.CharField(default='', help_text='热度值', max_length=50, verbose_name='热度值')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '抖音热搜',
                'verbose_name_plural': '抖音热搜',
                'db_table': 'spider_tiktok_hot_search',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
