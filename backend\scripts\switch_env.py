#!/usr/bin/env python
"""
环境切换脚本
用于在生产和测试环境之间切换
"""

import os
import sys
import argparse
from pathlib import Path

def switch_environment(env_type):
    """
    切换环境配置
    
    Args:
        env_type (str): 环境类型 ('test' 或 'production')
    """
    # 设置环境变量
    os.environ['DJANGO_ENV'] = env_type
    
    print(f"已切换到 {env_type} 环境")
    print(f"DJANGO_ENV = {os.environ.get('DJANGO_ENV')}")
    
    if env_type == 'test':
        print("使用测试数据库: myweb_dev")
        print("Celery日志将输出到: backend/logs/celery/")
    else:
        print("使用生产数据库: myweb")
        print("Celery日志将输出到: backend/logs/")

def main():
    parser = argparse.ArgumentParser(description='环境切换脚本')
    parser.add_argument('environment', choices=['test', 'production'], 
                       help='要切换到的环境 (test 或 production)')
    
    args = parser.parse_args()
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # 设置Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuadmin.settings')
    
    # 切换环境
    switch_environment(args.environment)
    
    print("\n使用方法:")
    print("1. 启动Celery Worker:")
    print("   python manage.py celery worker --loglevel=info")
    print("2. 启动Celery Beat:")
    print("   python manage.py celery beat --loglevel=info")
    print("3. 或者使用Docker:")
    print("   docker-compose up celery")

if __name__ == '__main__':
    main() 