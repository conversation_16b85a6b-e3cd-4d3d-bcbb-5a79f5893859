import json
import logging
import os
from datetime import datetime

import requests
from celery import shared_task
from django.conf import settings

from fuadmin.celery import app
from spiders.models import WeiboHotSearch

# 配置日志
logger = logging.getLogger(__name__)

@app.task(name='spiders.weibo.get_weibo_hot', bind=True)
def get_weibo_hot(self):
    """
    获取微博热搜数据 - ODS数据层存储
    每10分钟执行一次，用于存档分析
    """
    url = 'https://weibo.com/ajax/side/hotSearch'
    
    try:
        logger.info(f"开始获取微博热搜数据 - 任务ID: {self.request.id}")
        
        # 设置请求头，模拟浏览器访问
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://weibo.com/',
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            try:
                hot_json = response.json()
                realtimes = hot_json.get('data', {}).get('realtime', [])
                
                if realtimes:
                    saved_count = 0
                    current_time = datetime.now()
                    
                    for realtime in realtimes:
                        try:
                            # 修复字段名：hot_tile -> hot_title
                            data = {
                                'hot_index': realtime.get('rank', 0),
                                'hot_title': realtime.get('word', ''),  # 修复字段名
                                'label_name': realtime.get('label_name', ''),
                                'num': realtime.get('num', 0),
                                'flag_desc': realtime.get('flag_desc', ''),
                                'word_scheme': realtime.get('word_scheme', '')
                            }
                            
                            # 直接保存，不做去重处理 - ODS数据层存储
                            WeiboHotSearch.objects.create(
                                hot_index=data['hot_index'],
                                hot_title=data['hot_title'],  # 修复字段名
                                label_name=data['label_name'],
                                num=data['num'],
                                flag_desc=data['flag_desc'],
                                word_scheme=data['word_scheme']
                            )
                            saved_count += 1
                            logger.info(f"已保存热搜词: {data['hot_title']} (排名: {data['hot_index']}) - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                                
                        except Exception as e:
                            logger.error(f"保存单个热搜记录失败: {e}")
                            continue
                    
                    logger.info(f"微博热搜数据获取完成，共保存 {saved_count} 条记录 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    return f"成功保存 {saved_count} 条热搜记录 - ODS数据层存储"
                else:
                    logger.warning("没有获取到热搜数据")
                    return "没有热搜数据"
                    
            except (ValueError, KeyError) as e:
                error_msg = f"解析JSON失败: {e}"
                logger.error(error_msg)
                # 记录响应内容用于调试
                logger.error(f"响应内容: {response.text[:500]}")
                raise Exception(error_msg)
        else:
            error_msg = f"请求失败，状态码: {response.status_code}"
            logger.error(error_msg)
            raise Exception(error_msg)
            
    except requests.exceptions.Timeout:
        error_msg = "请求超时"
        logger.error(error_msg)
        raise Exception(error_msg)
    except requests.exceptions.RequestException as e:
        error_msg = f"网络请求异常: {e}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"未知错误: {e}"
        logger.error(error_msg)
        raise Exception(error_msg)

