# Generated by Django 4.0.8 on 2025-05-31 22:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Demo',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='项目名称', max_length=64, verbose_name='项目名称')),
                ('code', models.CharField(help_text='项目编码', max_length=32, verbose_name='项目编码')),
                ('status', models.CharField(help_text='项目状态', max_length=64, verbose_name='项目状态')),
                ('test', models.CharField(blank=True, help_text='test', max_length=64, null=True, verbose_name='test')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '核心模型',
                'verbose_name_plural': '核心模型',
                'abstract': False,
            },
        ),
    ]
