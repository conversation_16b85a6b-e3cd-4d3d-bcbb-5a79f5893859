from pywinauto import Application
from pywinauto.findwindows import WindowNotFoundError
import time


def monitor_qq_background():
    try:
        # 使用win32 backend连接更稳定
        app = Application(backend='win32').connect(title_re='QQ.*', visible_only=False)
        main_win = app.window(title_re='QQ.*')

        # 确保窗口不是最小化状态
        if main_win.is_minimized():
            main_win.restore()

        # 更精确的消息列表控件定位
        msg_list = main_win.child_window(
            class_name="CChatCtrl",
            control_type="List"
        )

        last_msg = ""
        while True:
            try:
                # 获取所有列表项（可能需要根据实际消息数量调整范围）
                items = msg_list.item_texts()[:5]  # 取最近5条消息
                current_msg = items[0] if items else ""

                if current_msg and current_msg != last_msg:
                    print(f"新消息: {current_msg}")
                    last_msg = current_msg

            except WindowNotFoundError:
                print("窗口连接丢失，尝试重新连接...")
                app = Application(backend='win32').connect(title_re='QQ.*', visible_only=False)
                main_win = app.window(title_re='QQ.*')
                msg_list = main_win.child_window(class_name="CChatCtrl", control_type="List")

            time.sleep(1)

    except Exception as e:
        print(f"发生错误: {str(e)}")
        input("按Enter键退出...")


if __name__ == "__main__":
    monitor_qq_background()
