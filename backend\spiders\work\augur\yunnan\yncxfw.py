import requests
import threading

# 初始化全局计数器
current_users = 0
max_users = 500
base_url = "https://yncxfw.com:8000/farmhouse-open/publicPlatFormIndex#/"

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
}

def visit_website():
    try:
        response = requests.get(base_url,  headers=headers, timeout=10,verify=False)
        print(f"访问成功，状态码：{response.status_code}")
        print(response.text)
    except Exception as e:
        print(f"访问失败：{str(e)}")


def batch_visit(increment=5):
    global current_users
    if current_users >= max_users:
        print("已达到最大用户数50，停止增加")
        return

    # 每次递增5个用户（可调整）
    new_users = min(increment, max_users - current_users)
    threads = []

    for _ in range(new_users):
        t = threading.Thread(target=visit_website)
        threads.append(t)
        t.start()

    current_users += new_users
    print(f"当前模拟用户数：{current_users}/{max_users}")

    # 等待所有线程完成
    for t in threads:
        t.join()

if __name__ == '__main__':
    batch_visit(increment=1)