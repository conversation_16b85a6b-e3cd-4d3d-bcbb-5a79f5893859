"""
tiktok数据库管理类
"""
import logging

from django.db import transaction
from django.db.models import Q

from spiders.models import TiktokLink

logger = logging.getLogger(__name__)
class TiktokDBManager:
    @staticmethod
    def get_pending_links(limit=10):
        """获取待处理的下载链接"""
        with transaction.atomic():
            # 使用select_for_update锁定记录，防止并发冲突
            queryset = TiktokLink.objects.select_for_update(skip_locked=True,nowait=False).filter(
                Q( download_status='0') | Q(download_status='2')| Q(download_status='4')
            ).order_by('-shares')[:limit]
            return list(queryset)

    @staticmethod
    def update_download_status(link_obj, status):
        """更新下载状态"""
        try:
            with transaction.atomic():
                link_obj.download_status = status
                link_obj.save(update_fields=['download_status', 'update_datetime'])
        except Exception as e:
            logger.error(f'更新状态失败{e}')


