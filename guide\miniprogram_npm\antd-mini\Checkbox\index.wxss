.ant-checkbox-item {
  color: var(--checkbox-text-color, #333333);
  margin-right: 16rpx;
  height: 59.6rpx;
  line-height: 59.6rpx;
  display: inline-block;
}
.ant-checkbox-item-container {
  display: flex;
  align-items: center;
}
.ant-checkbox-item-content {
  padding-left: 10rpx;
  text-align: left;
}
.ant-checkbox-item-wrap {
  position: relative;
  width: 44rpx;
  height: 44rpx;
  flex: 0 0 44rpx;
}
.ant-checkbox-item-base {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}
.ant-checkbox-item-fake {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ant-checkbox-item-fake-icon {
  background-color: var(--checkbox-fake-icon-background-color, #ffffff);
  border: 2rpx solid var(--checkbox-border-color, #cccccc);
  border-radius: 50vh;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.ant-checkbox-item-fake-checkedIcon {
  border-radius: 50vh;
  background-color: var(--checkbox-background-color, #1677ff);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ant-checkbox-item-fake-checkedIcon-icon {
  color: #ffffff;
  font-size: 28rpx;
}
.ant-checkbox-item-fake-disbaledIcon {
  box-sizing: border-box;
  border: 2rpx solid var(--checkbox-border-color, #cccccc);
  border-radius: 50vh;
  width: 100%;
  height: 100%;
  background-color: var(--checkbox-disabled-background, #f5f5f5);
}
.ant-checkbox-item-fake-disabledCheckedIcon {
  box-sizing: border-box;
  border: 2rpx solid var(--checkbox-border-color, #cccccc);
  background-color: var(--checkbox-disabled-background, #f5f5f5);
  border-radius: 50vh;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ant-checkbox-item-fake-disabledCheckedIcon-icon {
  color: var(--checkbox-border-color, #cccccc);
  font-size: 28rpx;
}
.ant-checkbox-item-disabled {
  opacity: 0.4;
}
