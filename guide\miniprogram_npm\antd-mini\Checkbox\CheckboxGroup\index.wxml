<wxs
  src="../index.wxs"
  module="componentUtils"
></wxs>
<list
  className="ant-checkbox-group {{className ? className : ''}} ant-checkbox-group-{{position}}"
  style="{{style}}"
>
  <checkbox-group
    name="{{name}}"
    value="{{mixin.value}}"
  >
    <view class="ant-checkbox-group-body">
      <block wx:if="{{position === 'vertical'}}">
        <block
          wx:for="{{options}}"
          wx:for-index="index"
          wx:for-item="item"
          wx:key="value"
        >
          <list-item>
            <ant-checkbox
              color="{{color}}"
              checked="{{componentUtils.getCheckboxChecked(item, mixin.value)}}"
              data-index="{{index}}"
              value="{{item.value}}"
              disabled="{{disabled || item.disabled}}"
              bindchange="onChange"
            >
              <view class="ant-checkbox-group-item-label-default">{{item.label}}</view>
            </ant-checkbox>
          </list-item>
        </block>
      </block>
      <block wx:else>
        <block
          wx:for="{{options}}"
          wx:for-index="index"
          wx:for-item="item"
          wx:key="value"
        >
          <ant-checkbox
            color="{{color}}"
            checked="{{componentUtils.getCheckboxChecked(item, mixin.value)}}"
            data-index="{{index}}"
            value="{{item.value}}"
            disabled="{{disabled || item.disabled}}"
            bindchange="onChange"
          >
            <view class="ant-checkbox-group-item-label-default">{{item.label}}</view>
          </ant-checkbox>
        </block>
      </block>
    </view>
  </checkbox-group>
</list>