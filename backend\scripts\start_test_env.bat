@echo off
chcp 65001 >nul
echo 启动测试环境...

REM 设置环境变量
set DJANGO_ENV=test
set DJANGO_SETTINGS_MODULE=fuadmin.settings_test

echo 环境变量已设置:
echo DJANGO_ENV=%DJANGO_ENV%
echo DJANGO_SETTINGS_MODULE=%DJANGO_SETTINGS_MODULE%

echo.
echo 正在启动Celery Worker...
start "Celery Worker" cmd /k "python manage.py celery worker --loglevel=info"

echo.
echo 正在启动Celery Beat...
start "Celery Beat" cmd /k "python manage.py celery beat --loglevel=info"

echo.
echo 测试环境已启动！
echo 日志文件位置: backend/logs/celery/
echo 数据库: myweb_dev
echo.
echo 按任意键退出...
pause > nul 