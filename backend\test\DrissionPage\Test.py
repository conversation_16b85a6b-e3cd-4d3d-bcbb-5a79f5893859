from DrissionPage import ChromiumPage,Chromium,ChromiumOptions
from DrissionPage.errors import ElementLostError

if __name__ == '__main__':
    chrome = Chromium()
    tab1 = chrome.new_tab()
    tab2 = chrome.new_tab()
    tab3 = chrome.new_tab()
    tab4 = chrome.new_tab()
    tab1.get('https://www.baidu.com')
    tab2.get('https://www.zhihu.com')
    tab3.get('https://www.bing.com')

    try:
        input_selector1 = '@@tag()=input@@id=kw'  # 改用CSS选择器
        tab1.wait.eles_loaded(input_selector1, timeout=10)
        url_input1 = tab1.ele(input_selector1)
        url_input1.input(clear=True, vals='搜索关键字')
        print('成功输入URL')

        input_selector2 = '@@tag()=input@@name=username'  # 改用CSS选择器
        tab2.wait.eles_loaded(input_selector2, timeout=10)
        url_input2 = tab2.ele(input_selector2)
        url_input2.input(clear=True, vals='搜索关键字')
        print('成功输入URL')
    except ElementLostError as e:
        print('URL输入框定位失败')
        raise RuntimeError('页面元素加载异常') from e







