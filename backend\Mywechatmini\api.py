# 设置过滤字段
from typing import List, Optional

from Mywechatmini.models import MyWechat
from utils.fu_ninja import FuFilters, MyPagination
from ninja import Field, ModelSchema, Query, Router, Body
from ninja.pagination import paginate
from utils.fu_crud import (
    ImportSchema,
    create,
    delete,
    export_data,
    import_data,
    retrieve,
    update,
)

router = Router()

class Filters(FuFilters):
    name: str = Field(None, alias="name")
    openid: str = Field(None, alias="openid")
    publish_status: bool = Field(None, alias="publish_status")
    id: str = Field(None, alias="wechat_id")
    class_id: str = Field(None, alias="class_id")


# 设置请求接收字段
class MyWechatMiniSchemaIn(ModelSchema):
    class Config:
        model = MyWechat
        model_fields = ['name', 'openid', 'short_desc', 'desc', 'publish_status', 'icon', 'class_id']


# 设置更新请求字段
class MyWechatMiniUpdateSchemaIn(ModelSchema):
    id: int
    class Config:
        model = MyWechat
        model_fields = ['id', 'name', 'openid', 'short_desc', 'desc', 'publish_status', 'icon', 'class_id']


# 设置响应字段
class MyWechatMiniSchemaOut(ModelSchema):
    class Config:
        model = MyWechat
        model_fields = ['id', 'name', 'openid', 'icon', 'publish_status', 'desc', 'short_desc', 'class_id', 'create_datetime']


# 获取小程序列表
@router.get("/wechatMiniPros", response=List[MyWechatMiniSchemaOut])
@paginate(MyPagination)
def list_wechat_mini_pros(request, filters: Filters = Query(...)):
    qs = retrieve(request, MyWechat, filters)
    return qs


# 创建小程序
@router.post("/wechatMiniPros", response=MyWechatMiniSchemaOut)
def create_wechat_mini_pro(request, data: MyWechatMiniSchemaIn = Body(...)):
    wechat_mini = create(request, MyWechat, data.dict())
    return wechat_mini


# 更新小程序
@router.put("/wechatMiniPros/{wechat_id}", response=MyWechatMiniSchemaOut)
def update_wechat_mini_pro(request, wechat_id: int, data: MyWechatMiniUpdateSchemaIn = Body(...)):
    # 确保请求中的ID与路径中的ID一致
    if data.id != wechat_id:
        raise ValueError("ID in request body does not match ID in path")
    wechat_mini = update(request, MyWechat, wechat_id, data.dict())
    return wechat_mini


# 删除小程序
@router.delete("/wechatMiniPros/{wechat_id}", response={204: None})
def delete_wechat_mini_pro(request, wechat_id: int):
    delete(request, MyWechat, wechat_id)
    return 204, None


# 获取单个小程序
@router.get("/wechatMiniPros/{wechat_id}", response=MyWechatMiniSchemaOut)
def get_wechat_mini_pro(request, wechat_id: int):
    wechat_mini = MyWechat.objects.get(id=wechat_id)
    return wechat_mini
