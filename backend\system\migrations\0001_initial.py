# Generated by Django 4.0.8 on 2025-01-09 15:59

from django.conf import settings
import django.contrib.auth.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import system.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Users',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('username', models.CharField(db_index=True, help_text='用户账号', max_length=150, unique=True, verbose_name='用户账号')),
                ('email', models.EmailField(blank=True, help_text='邮箱', max_length=255, null=True, verbose_name='邮箱')),
                ('mobile', models.CharField(blank=True, help_text='电话', max_length=255, null=True, verbose_name='电话')),
                ('avatar', models.TextField(blank=True, help_text='头像', null=True, verbose_name='头像')),
                ('name', models.CharField(help_text='姓名', max_length=40, verbose_name='姓名')),
                ('status', models.BooleanField(default=True, help_text='状态', verbose_name='状态')),
                ('gender', models.IntegerField(blank=True, choices=[(0, '女'), (1, '男')], default=1, help_text='性别', null=True, verbose_name='性别')),
                ('user_type', models.IntegerField(blank=True, choices=[(0, '后台用户'), (1, '前台用户')], default=0, help_text='用户类型', null=True, verbose_name='用户类型')),
                ('first_name', models.CharField(blank=True, max_length=150, null=True)),
                ('last_name', models.CharField(blank=True, max_length=150, null=True)),
                ('home_path', models.CharField(blank=True, max_length=150, null=True)),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '用户表',
                'verbose_name_plural': '用户表',
                'db_table': 'system_users',
                'ordering': ('-create_datetime',),
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Dept',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='部门名称', max_length=64, verbose_name='部门名称')),
                ('owner', models.CharField(blank=True, help_text='负责人', max_length=32, null=True, verbose_name='负责人')),
                ('phone', models.CharField(blank=True, help_text='联系电话', max_length=32, null=True, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, help_text='邮箱', max_length=32, null=True, verbose_name='邮箱')),
                ('status', models.BooleanField(blank=True, default=True, help_text='部门状态', null=True, verbose_name='部门状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, default=None, help_text='上级部门', null=True, on_delete=django.db.models.deletion.PROTECT, to='system.dept', verbose_name='上级部门')),
            ],
            options={
                'verbose_name': '部门表',
                'verbose_name_plural': '部门表',
                'db_table': 'system_dept',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Dict',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(blank=True, help_text='字典名称', max_length=100, null=True, verbose_name='字典名称')),
                ('code', models.CharField(blank=True, help_text='编码', max_length=100, null=True, verbose_name='编码')),
                ('status', models.BooleanField(blank=True, default=True, help_text='状态', verbose_name='状态')),
                ('remark', models.CharField(blank=True, help_text='备注', max_length=2000, null=True, verbose_name='备注')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '字典表',
                'verbose_name_plural': '字典表',
                'db_table': 'system_dict',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Menu',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('icon', models.CharField(default='ant-design:book-outlined', help_text='菜单图标', max_length=64, verbose_name='菜单图标')),
                ('title', models.CharField(help_text='菜单名称', max_length=64, verbose_name='菜单名称')),
                ('permission', models.CharField(blank=True, help_text='权限标识', max_length=64, null=True, verbose_name='权限标识')),
                ('is_ext', models.BooleanField(default=False, help_text='是否外链', verbose_name='是否外链')),
                ('type', models.IntegerField(choices=[(0, '目录'), (1, '菜单')], help_text='是否目录', verbose_name='是否目录')),
                ('path', models.CharField(blank=True, help_text='路由地址', max_length=128, null=True, verbose_name='路由地址')),
                ('redirect', models.CharField(blank=True, help_text='重定向地址', max_length=128, null=True, verbose_name='重定向地址')),
                ('component', models.CharField(blank=True, help_text='组件地址', max_length=128, null=True, verbose_name='组件地址')),
                ('name', models.CharField(blank=True, help_text='组件名称', max_length=50, null=True, verbose_name='组件名称')),
                ('status', models.BooleanField(blank=True, default=True, help_text='菜单状态', verbose_name='菜单状态')),
                ('keepalive', models.BooleanField(blank=True, default=False, help_text='是否页面缓存', verbose_name='是否页面缓存')),
                ('hide_menu', models.BooleanField(blank=True, default=False, help_text='侧边栏中是否隐藏', verbose_name='侧边栏中是否隐藏')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='上级菜单', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.menu', verbose_name='上级菜单')),
            ],
            options={
                'verbose_name': '菜单表',
                'verbose_name_plural': '菜单表',
                'db_table': 'system_menu',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='MenuButton',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='名称', max_length=64, verbose_name='名称')),
                ('code', models.CharField(help_text='权限值', max_length=64, verbose_name='权限值')),
                ('api', models.CharField(help_text='接口地址', max_length=200, verbose_name='接口地址')),
                ('method', models.IntegerField(blank=True, default=0, help_text='接口请求方法', null=True, verbose_name='接口请求方法')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, help_text='关联菜单', on_delete=django.db.models.deletion.CASCADE, related_name='menuPermission', to='system.menu', verbose_name='关联菜单')),
            ],
            options={
                'verbose_name': '菜单权限表',
                'verbose_name_plural': '菜单权限表',
                'db_table': 'system_menu_button',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='MenuColumnField',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='名称', max_length=64, verbose_name='名称')),
                ('code', models.CharField(help_text='权限值', max_length=64, verbose_name='权限值')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, help_text='关联菜单', on_delete=django.db.models.deletion.CASCADE, related_name='menuColumnField', to='system.menu', verbose_name='关联菜单')),
            ],
            options={
                'verbose_name': '菜单数据列表',
                'verbose_name_plural': '菜单数据列表',
                'db_table': 'system_menu_column_field',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('title', models.CharField(help_text='标题', max_length=50, verbose_name='标题')),
                ('key', models.CharField(help_text='键', max_length=20, verbose_name='键')),
                ('value', models.JSONField(blank=True, help_text='值', max_length=100, null=True, verbose_name='值')),
                ('status', models.BooleanField(default=False, help_text='启用状态', verbose_name='启用状态')),
                ('data_options', models.JSONField(blank=True, help_text='数据options', null=True, verbose_name='数据options')),
                ('form_item_type', models.IntegerField(blank=True, choices=[(0, 'text'), (1, 'textarea'), (2, 'number'), (3, 'select'), (4, 'radio'), (5, 'checkbox'), (6, 'date'), (7, 'datetime'), (8, 'time'), (9, 'imgs'), (10, 'files'), (11, 'array'), (12, 'foreignkey'), (13, 'manytomany')], default=0, help_text='表单类型', verbose_name='表单类型')),
                ('rule', models.JSONField(blank=True, help_text='校验规则', null=True, verbose_name='校验规则')),
                ('placeholder', models.CharField(blank=True, help_text='提示信息', max_length=50, null=True, verbose_name='提示信息')),
                ('setting', models.JSONField(blank=True, help_text='配置', null=True, verbose_name='配置')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='父级', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.systemconfig', verbose_name='父级')),
            ],
            options={
                'verbose_name': '系统配置表',
                'verbose_name_plural': '系统配置表',
                'db_table': 'system_config',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='角色名称', max_length=64, verbose_name='角色名称')),
                ('code', models.CharField(help_text='角色编码', max_length=64, unique=True, verbose_name='角色编码')),
                ('status', models.BooleanField(default=True, help_text='角色状态', verbose_name='角色状态')),
                ('admin', models.BooleanField(default=False, help_text='是否为admin', verbose_name='是否为admin')),
                ('data_range', models.IntegerField(choices=[(0, '仅本人数据权限'), (1, '本部门数据权限'), (2, '本部门及以下数据权限'), (3, '全部数据权限'), (4, '自定数据权限')], default=0, help_text='数据权限范围', verbose_name='数据权限范围')),
                ('column', models.ManyToManyField(db_constraint=False, help_text='列表权限', to='system.menucolumnfield', verbose_name='列表权限')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('dept', models.ManyToManyField(db_constraint=False, help_text='数据权限-关联部门', to='system.dept', verbose_name='数据权限-关联部门')),
                ('menu', models.ManyToManyField(db_constraint=False, help_text='关联菜单', to='system.menu', verbose_name='关联菜单')),
                ('permission', models.ManyToManyField(db_constraint=False, help_text='关联菜单的接口按钮', to='system.menubutton', verbose_name='关联菜单的接口按钮')),
            ],
            options={
                'verbose_name': '角色表',
                'verbose_name_plural': '角色表',
                'db_table': 'system_role',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='岗位名称', max_length=64, verbose_name='岗位名称')),
                ('code', models.CharField(help_text='岗位编码', max_length=32, verbose_name='岗位编码')),
                ('status', models.IntegerField(choices=[(0, '离职'), (1, '在职')], default=1, help_text='岗位状态', verbose_name='岗位状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '岗位表',
                'verbose_name_plural': '岗位表',
                'db_table': 'system_post',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='OperationLog',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('request_username', models.CharField(blank=True, help_text='请求用户', max_length=50, null=True, verbose_name='请求用户')),
                ('request_modular', models.CharField(blank=True, help_text='请求模块', max_length=64, null=True, verbose_name='请求模块')),
                ('request_path', models.CharField(blank=True, help_text='请求地址', max_length=400, null=True, verbose_name='请求地址')),
                ('request_body', models.TextField(blank=True, help_text='请求参数', null=True, verbose_name='请求参数')),
                ('request_method', models.CharField(blank=True, help_text='请求方式', max_length=8, null=True, verbose_name='请求方式')),
                ('request_msg', models.TextField(blank=True, help_text='操作说明', null=True, verbose_name='操作说明')),
                ('request_ip', models.CharField(blank=True, help_text='请求ip地址', max_length=32, null=True, verbose_name='请求ip地址')),
                ('request_browser', models.CharField(blank=True, help_text='请求浏览器', max_length=64, null=True, verbose_name='请求浏览器')),
                ('response_code', models.CharField(blank=True, help_text='响应状态码', max_length=32, null=True, verbose_name='响应状态码')),
                ('request_os', models.CharField(blank=True, help_text='操作系统', max_length=64, null=True, verbose_name='操作系统')),
                ('json_result', models.TextField(blank=True, help_text='返回信息', null=True, verbose_name='返回信息')),
                ('status', models.BooleanField(default=False, help_text='响应状态', verbose_name='响应状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '操作日志',
                'verbose_name_plural': '操作日志',
                'db_table': 'system_operation_log',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('username', models.CharField(blank=True, help_text='登录用户名', max_length=32, null=True, verbose_name='登录用户名')),
                ('ip', models.CharField(blank=True, help_text='登录ip', max_length=32, null=True, verbose_name='登录ip')),
                ('agent', models.TextField(blank=True, help_text='agent信息', null=True, verbose_name='agent信息')),
                ('browser', models.CharField(blank=True, help_text='浏览器名', max_length=200, null=True, verbose_name='浏览器名')),
                ('os', models.CharField(blank=True, help_text='操作系统', max_length=200, null=True, verbose_name='操作系统')),
                ('continent', models.CharField(blank=True, help_text='州', max_length=50, null=True, verbose_name='州')),
                ('country', models.CharField(blank=True, help_text='国家', max_length=50, null=True, verbose_name='国家')),
                ('province', models.CharField(blank=True, help_text='省份', max_length=50, null=True, verbose_name='省份')),
                ('city', models.CharField(blank=True, help_text='城市', max_length=50, null=True, verbose_name='城市')),
                ('district', models.CharField(blank=True, help_text='县区', max_length=50, null=True, verbose_name='县区')),
                ('isp', models.CharField(blank=True, help_text='运营商', max_length=50, null=True, verbose_name='运营商')),
                ('area_code', models.CharField(blank=True, help_text='区域代码', max_length=50, null=True, verbose_name='区域代码')),
                ('country_english', models.CharField(blank=True, help_text='英文全称', max_length=50, null=True, verbose_name='英文全称')),
                ('country_code', models.CharField(blank=True, help_text='简称', max_length=50, null=True, verbose_name='简称')),
                ('longitude', models.CharField(blank=True, help_text='经度', max_length=50, null=True, verbose_name='经度')),
                ('latitude', models.CharField(blank=True, help_text='纬度', max_length=50, null=True, verbose_name='纬度')),
                ('login_type', models.IntegerField(choices=[(1, '普通登录')], default=1, help_text='登录类型', verbose_name='登录类型')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'db_table': 'system_login_log',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='GeneratorTemplate',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='模板名称', max_length=64, verbose_name='模板名称')),
                ('code', models.CharField(help_text='模板编码', max_length=32, verbose_name='模板编码')),
                ('form_info', models.TextField(help_text='表单信息', verbose_name='表单信息')),
                ('table_info', models.TextField(help_text='表格信息', verbose_name='表格信息')),
                ('has_menu', models.BooleanField(default=False, help_text='是否已经生成菜单', verbose_name='是否已经生成菜单')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '代码生成器模板',
                'verbose_name_plural': '代码生成器模板',
                'db_table': 'system_generator_template',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='File',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(blank=True, help_text='实际名称', max_length=255, null=True, verbose_name='实际名称')),
                ('save_name', models.CharField(blank=True, help_text='存储名称', max_length=255, null=True, verbose_name='存储名称')),
                ('url', models.FileField(upload_to=system.models.media_file_name)),
                ('size', models.BigIntegerField(blank=True, help_text='大小', null=True, verbose_name='大小')),
                ('md5sum', models.CharField(blank=True, help_text='文件md5', max_length=36, verbose_name='文件md5')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '文件管理',
                'verbose_name_plural': '文件管理',
                'db_table': 'system_file',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='DictItem',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('icon', models.CharField(blank=True, help_text='ICON', max_length=100, null=True, verbose_name='ICON')),
                ('label', models.CharField(blank=True, help_text='显示名称', max_length=100, null=True, verbose_name='显示名称')),
                ('value', models.CharField(blank=True, help_text='实际值', max_length=100, null=True, verbose_name='实际值')),
                ('status', models.BooleanField(blank=True, default=True, help_text='状态', verbose_name='状态')),
                ('remark', models.CharField(blank=True, help_text='备注', max_length=2000, null=True, verbose_name='备注')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('dict', models.ForeignKey(db_constraint=False, help_text='字典', on_delete=django.db.models.deletion.CASCADE, related_name='dictItem', to='system.dict')),
            ],
            options={
                'verbose_name': '字典表详情表',
                'verbose_name_plural': '字典表详情表',
                'db_table': 'system_dict_item',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='CategoryDict',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('label', models.CharField(blank=True, help_text='显示名称', max_length=100, null=True, verbose_name='显示名称')),
                ('value', models.CharField(blank=True, help_text='实际值', max_length=100, null=True, verbose_name='实际值')),
                ('code', models.CharField(blank=True, help_text='编码', max_length=100, null=True, unique=True, verbose_name='编码')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, default=None, help_text='上级', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.categorydict', verbose_name='上级')),
            ],
            options={
                'verbose_name': '分类字典表',
                'verbose_name_plural': '分类字典表',
                'db_table': 'system_category_dict',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Button',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='权限名称', max_length=64, unique=True, verbose_name='权限名称')),
                ('code', models.CharField(help_text='权限值', max_length=64, unique=True, verbose_name='权限值')),
                ('status', models.BooleanField(blank=True, default=True, help_text='按钮状态', null=True, verbose_name='按钮状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '权限标识表',
                'verbose_name_plural': '权限标识表',
                'db_table': 'system_button',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Area',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('name', models.CharField(help_text='名称', max_length=100, verbose_name='名称')),
                ('code', models.CharField(db_index=True, help_text='地区编码', max_length=20, unique=True, verbose_name='地区编码')),
                ('level', models.BigIntegerField(help_text='地区层级(1省份 2城市 3区县 4乡级)', verbose_name='地区层级(1省份 2城市 3区县 4乡级)')),
                ('pinyin', models.CharField(help_text='拼音', max_length=255, verbose_name='拼音')),
                ('initials', models.CharField(help_text='首字母', max_length=20, verbose_name='首字母')),
                ('enable', models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('pcode', models.ForeignKey(blank=True, db_constraint=False, help_text='父地区编码', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.area', to_field='code', verbose_name='父地区编码')),
            ],
            options={
                'verbose_name': '地区表',
                'verbose_name_plural': '地区表',
                'db_table': 'system_area',
                'ordering': ('code',),
            },
        ),
        migrations.CreateModel(
            name='ApiWhiteList',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('remark', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('belong_dept', models.IntegerField(blank=True, help_text='数据归属部门', null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('url', models.CharField(help_text='url地址', max_length=200, verbose_name='url')),
                ('method', models.IntegerField(blank=True, default=0, help_text='接口请求方法', null=True, verbose_name='接口请求方法')),
                ('enable_datasource', models.BooleanField(blank=True, default=True, help_text='激活数据权限', verbose_name='激活数据权限')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '接口白名单',
                'verbose_name_plural': '接口白名单',
                'db_table': 'system_api_white_list',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.AddField(
            model_name='users',
            name='dept',
            field=models.ForeignKey(blank=True, db_constraint=False, help_text='关联部门', null=True, on_delete=django.db.models.deletion.SET_NULL, to='system.dept', verbose_name='所属部门'),
        ),
        migrations.AddField(
            model_name='users',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='users',
            name='post',
            field=models.ManyToManyField(db_constraint=False, help_text='关联岗位', to='system.post', verbose_name='关联岗位'),
        ),
        migrations.AddField(
            model_name='users',
            name='role',
            field=models.ManyToManyField(db_constraint=False, help_text='关联角色', to='system.role', verbose_name='关联角色'),
        ),
        migrations.AddField(
            model_name='users',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions'),
        ),
    ]
