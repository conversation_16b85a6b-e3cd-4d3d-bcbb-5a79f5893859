# core/tasks.py
from django.db import models

from utils.models import CoreModel


class BaseBrowserTask:
    """浏览器任务基类（所有Drissionpage任务继承此类）"""

    def __init__(self):
        self.browser = None  # Drissionpage浏览器实例
        self.max_retries = 3  # 默认重试次数

    def init_browser(self):
        """初始化浏览器配置"""
        # 包含代理设置、窗口大小、UserAgent等初始化逻辑
        # 异常处理机制

    def safe_close(self):
        """安全关闭浏览器"""

    def retry_logic(self, func, *args, **kwargs):
        """通用重试逻辑装饰器"""


# core/models.py
class VideoLink(CoreModel):
    """视频链接存储模型"""
    STATUS_CHOICES = (('pending', '待处理'), ('downloaded', '已下载'), ('failed', '失败'))
    url = models.URLField(unique=True)
    source_type = models.CharField(choices=(('recommend', '推荐'), ('explore', '探索')))
    status = models.CharField(choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    download_retries = models.IntegerField(default=0)


class VideoDownloadRecord(models.Model):
    """下载记录模型"""
    video = models.ForeignKey(VideoLink, on_delete=models.CASCADE)
    download_path = models.FilePathField()
    download_source = models.CharField(choices=(('savetk', 'savetk.net'), ('dlpanda', 'Dlpanda.com')))
    status = models.BooleanField(default=False)
    timestamp = models.DateTimeField(auto_now_add=True)


class VideoPublishRecord(models.Model):
    """视频发布记录模型"""
    # 包含发布状态、时间、抖音ID等信息