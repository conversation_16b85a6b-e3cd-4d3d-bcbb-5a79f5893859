#!/usr/bin/env python
"""
微博热搜数据分析脚本
展示如何使用ODS数据进行热搜分析
"""

import os
import sys
import django
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuadmin.settings_test')
django.setup()

from spiders.models import WeiboHotSearch

def analyze_hot_search_trends():
    """
    分析热搜趋势
    """
    print("🔍 微博热搜数据分析")
    print("=" * 50)
    
    # 获取最近24小时的数据
    yesterday = datetime.now() - timedelta(days=1)
    recent_data = WeiboHotSearch.objects.filter(
        create_datetime__gte=yesterday
    ).order_by('create_datetime')
    
    print(f"📊 数据统计:")
    print(f"  分析时间范围: {yesterday.strftime('%Y-%m-%d %H:%M')} 至今")
    print(f"  总记录数: {recent_data.count()}")
    
    if recent_data.count() == 0:
        print("❌ 没有找到数据，请先运行爬虫获取数据")
        return
    
    # 1. 按时间分组统计
    time_groups = defaultdict(list)
    for record in recent_data:
        time_key = record.create_datetime.strftime('%Y-%m-%d %H:%M')
        time_groups[time_key].append(record)
    
    print(f"\n📅 数据采集时间点: {len(time_groups)} 个")
    
    # 2. 分析热搜词出现频率
    hot_words = Counter()
    for record in recent_data:
        hot_words[record.hot_title] += 1
    
    print(f"\n🔥 热搜词出现频率 (Top 10):")
    print("-" * 40)
    for word, count in hot_words.most_common(10):
        print(f"  {word}: {count} 次")
    
    # 3. 分析热搜持续时间
    word_duration = defaultdict(list)
    for record in recent_data:
        word_duration[record.hot_title].append(record.create_datetime)
    
    print(f"\n⏱️ 热搜持续时间分析:")
    print("-" * 40)
    for word, times in list(word_duration.items())[:5]:
        if len(times) > 1:
            duration = max(times) - min(times)
            hours = duration.total_seconds() / 3600
            print(f"  {word}: {hours:.1f} 小时 ({len(times)} 次出现)")
    
    # 4. 分析排名变化
    print(f"\n📈 排名变化分析:")
    print("-" * 40)
    for word, times in list(word_duration.items())[:3]:
        if len(times) > 1:
            records = [r for r in recent_data if r.hot_title == word]
            records.sort(key=lambda x: x.create_datetime)
            
            first_rank = records[0].hot_index
            last_rank = records[-1].hot_index
            best_rank = min(r.hot_index for r in records)
            
            print(f"  {word}:")
            print(f"    首次排名: {first_rank}, 最终排名: {last_rank}, 最佳排名: {best_rank}")
    
    # 5. 按标签统计
    label_stats = Counter()
    for record in recent_data:
        if record.label_name:
            label_stats[record.label_name] += 1
    
    print(f"\n🏷️ 热搜标签统计:")
    print("-" * 40)
    for label, count in label_stats.most_common(5):
        print(f"  {label}: {count} 次")
    
    # 6. 按分类统计
    category_stats = Counter()
    for record in recent_data:
        if record.flag_desc:
            category_stats[record.flag_desc] += 1
    
    print(f"\n📂 热搜分类统计:")
    print("-" * 40)
    for category, count in category_stats.most_common(5):
        print(f"  {category}: {count} 次")
    
    print(f"\n✅ 分析完成！")
    print(f"💡 提示: 这是基于ODS数据层的分析，可以进一步开发ETL处理到DM层")

def analyze_specific_hot_word(hot_word):
    """
    分析特定热搜词的详细情况
    """
    print(f"🔍 分析热搜词: {hot_word}")
    print("=" * 50)
    
    # 获取该热搜词的所有记录
    records = WeiboHotSearch.objects.filter(
        hot_title__icontains=hot_word
    ).order_by('create_datetime')
    
    if records.count() == 0:
        print(f"❌ 没有找到包含 '{hot_word}' 的热搜记录")
        return
    
    print(f"📊 找到 {records.count()} 条相关记录")
    
    # 按时间显示排名变化
    print(f"\n📈 排名变化:")
    print("-" * 30)
    for record in records:
        print(f"  {record.create_datetime.strftime('%Y-%m-%d %H:%M')}: 排名 {record.hot_index}")
    
    # 统计信息
    ranks = [r.hot_index for r in records]
    print(f"\n📊 统计信息:")
    print(f"  最佳排名: {min(ranks)}")
    print(f"  最差排名: {max(ranks)}")
    print(f"  平均排名: {sum(ranks) / len(ranks):.1f}")
    print(f"  持续时间: {(records.last().create_datetime - records.first().create_datetime).total_seconds() / 3600:.1f} 小时")

def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='微博热搜数据分析')
    parser.add_argument('--word', type=str, help='分析特定热搜词')
    
    args = parser.parse_args()
    
    if args.word:
        analyze_specific_hot_word(args.word)
    else:
        analyze_hot_search_trends()

if __name__ == '__main__':
    main() 