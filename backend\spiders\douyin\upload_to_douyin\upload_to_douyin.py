import json
import random
import time
from pathlib import Path

from DrissionPage import Chromium, ChromiumOptions
from DrissionPage.errors import ElementLostError


class DouYin:
    """初始化"""
    def __init__(self):
        self.download_dir = Path(r'E:\\datas\\files\\tiktok')
        self.options = ChromiumOptions()
        self._browser_option_setup()
        self.browser = Chromium(self.options)
        self._browser_config()
        self.base_url = 'https://creator.douyin.com/'
        self.tab = self.browser.latest_tab
        self.tab.get(self.base_url)
    """浏览器启动相关配置"""
    def _browser_option_setup(self):
        """配置当前浏览器参数"""
        self.options.headless(False)
        self.options.mute(True)
        # self.options.set_user_data_path(self.download_dir.joinpath('temp'))
        # self.options.set_argument('--window-size', '1920,1079')
        self.options.set_argument('--start-maximized')  # 设置启动时最大化
        self.options.set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
        self.options.set_local_port(random.randint(1000, 9999))
    """浏览器配置"""
    def _browser_config(self):
        self.browser.set.retry_times(5)
        self.browser.set.download_path(str(self.download_dir))
    """检测登录状态"""
    def check_login_status(self):
        """
        自定义登录状态检查
        :return: bool 是否已登录
        """
        # 示例：检查页面上的登录标志元素
        print('检测登录状态中。。。。')
        user_avatar_selector = '@@tag()=div@@id=header-avatar'
        creator_status = self.tab.wait.eles_loaded(user_avatar_selector,timeout=5)
        douyin_header_selector = '@@tag()=span@@data-e2e=live-avatar'
        douyin_header = self.tab.wait.eles_loaded(douyin_header_selector,timeout=5)
        print(creator_status)
        print(douyin_header)
        if creator_status or douyin_header:
            print('登录成功')
            return True
        else:
            print('登录好像失败了')
            return False
    """切换为密码登录"""
    def _switch_login_with_password(self):
        try:
            switch_login_with_password_button_selector = '@@tag()=span@@text()=密码登录 '
            if self.tab.wait.eles_loaded(switch_login_with_password_button_selector, timeout=5):
                self.tab.ele(switch_login_with_password_button_selector).click()
                return True
            else:
                return False
        except ElementLostError as e:
            print(e)
            return False
    """输入用户信息"""
    def _login_username(self,username:str):
        try:
            login_input_selector = '@@tag()=input@@id=number'
            if self.tab.wait.eles_loaded(login_input_selector,timeout=5):
                self.tab.ele(login_input_selector).input(username)
                print('输入用户名成果')
                return True
            else:
                print('输入用户失败')
                return False
        except ElementLostError as e:
            print(e)
            return False
    """输入密码"""
    def _login_password(self, password: str):
        try:
            login_input_selector = 'tag:input@id:code'
            if self.tab.wait.eles_loaded(login_input_selector, timeout=5):
                self.tab.ele(login_input_selector).input(password)
                return True
            else:
                return False
        except ElementLostError as e:
            print(e)
            return False
    """同意协议"""
    def _agreement(self):
        try:
            agreement_selector = "xpath://div[@class='agreement-FCwv7r login-agreement-Xc9ghd']//img"
            if self.tab.wait.eles_loaded(agreement_selector, timeout=5):
                self.tab.ele(agreement_selector).click()
                print('同意协议')
                return True
            else:
                print('点击同意协议失败')
                return False
        except ElementLostError as e:
            print(e)
            return False
        except Exception as e:
            print('点击同意协议未知异常')
            print(e)
    """点击发送验证码"""
    def _send_message_code(self):
        try:
            send_message_code_button_selector = 'tag:div@class:douyin-creator-vmock-input-suffix'
            if self.tab.wait.eles_loaded(send_message_code_button_selector, timeout=5):
                self.tab.ele(send_message_code_button_selector).click()
                print('点击发送验证码，暂停30秒')
                time.sleep(30)
                return True
            else:
                print('点击发送验证码失败')
                return False
        except ElementLostError as e:
            print(e)
            return False
        except Exception as e:
            print('点击发送验证码未知异常')
            print(e)
    """输入验证码"""
    def _message_code(self,password:str):
        try:
            message_code_input_selector = 'tag:input@type:submit'
            if self.tab.wait.eles_loaded(message_code_input_selector, timeout=5):
                self.tab.ele(message_code_input_selector).input(password)
                print()
                return True
            else:
                return False
        except ElementLostError as e:
            print('输入验证码异常')
            print(e)
            return False
        except Exception as e:
            print('输入验证码未知异常')
            print(e)
    """点击登录"""
    def _login_button(self):
        try:
            login_input_selector = 'tag:button@type:submit'
            if self.tab.wait.eles_loaded(login_input_selector, timeout=5):
                self.tab.ele(login_input_selector).click()
                print('点击登录')
                return True
            else:
                print('点击登录失败')
                return False
        except ElementLostError as e:
            print('点击登录失败按钮失败')
            print(e)
            return False
        except Exception as e:
            print('点击登录未知异常')
            print(e)
            return False
    """密码登录"""
    def login_with_passwd(self,username:str,password:str):
        self._switch_login_with_password()
        self._login_username(username)
        self._login_password(password)
        self._agreement()
        self._login_button()
    """验证码登录"""
    def log_with_message_code(self,username:str,message_code:str):
        print(message_code)
        self._login_username(username)
        self._agreement()
        self._send_message_code()
        self._login_button()

        """Cookie相关操作"""
    """获取保存cookie的路径"""
    def _get_cookie_path(self):
        return self.download_dir.joinpath('douyin_cookies.json')
    """保存cookie"""
    def save_cookies(self):
        """保存当前所有Cookie到文件"""
        cookies = self.browser.cookies()
        cookie_path  = str(self._get_cookie_path())
        with open(cookie_path, 'w', encoding='utf-8') as f:  # 添加编码参数
            f.write(json.dumps(cookies, ensure_ascii=False))
            f.close()
        print('Cookies已保存')
    """加载cookie"""
    def load_cookies(self):
        """从文件加载Cookies到浏览器"""
        cookie_path = self._get_cookie_path()
        if not cookie_path.exists():
            print('没找到cookie文件')
            return False
        try:
            with open(cookie_path, 'r') as f:
                cookies = json.load(f)
            self.browser.set.cookies(cookies)
            print('Cookies已加载')
            return True
        except Exception as e:
            print(f'加载Cookies失败: {str(e)}')
            return False
    """用cookie登录"""
    def try_cookie_login(self):
        """尝试使用Cookie登录"""
        if self.load_cookies():
            self.tab.get(self.base_url)
            if self.check_login_status():
                print("Cookie登录成功")
                return True
            print("Cookie已过期")
        return False
    """刷新页面"""
    def refresh(self):
        self.tab.refresh()
        time.sleep(10)
    """
    发布视频流程：
    _publish_video: 发布视频按钮
    _upload_video: 上传视频
    """
    """点击发布视频"""
    def _public_video(self):
        print('开始点击发布视频')
        try:
            publish_video_button_selector = 'tag:button@aria-haspopup:true'
            if self.tab.wait.eles_loaded(publish_video_button_selector, timeout=5):
                self.tab.ele(publish_video_button_selector).click()
                print('点击发布按钮成功')
                return True
            else:
                print('点击发布按钮视频失败')
        except ElementLostError as e:
            print(f'找不到发布视频按钮{e}')
            return False
        except Exception as e:
            print(f'点击发布视频未知异常：{e}')
            return False
    """上传视频"""
    def _upload_video(self):
        print('开始上传视频')
        try:
            upload_video_button_selector = 'tag:div@class:container-drag-title-p6mssi'
            if self.tab.wait.eles_loaded(upload_video_button_selector, timeout=5):
                self.tab.ele(upload_video_button_selector).click()
                print('点击发布按钮成功')
                return True
            else:
                print('点击发布按钮视频失败')
        except ElementLostError as e:
            print(f'找不到发布视频按钮{e}')
            return False
        except Exception as e:
            print(f'点击发布视频未知异常：{e}')
            return False
"""main方法测试"""
if __name__ == '__main__':
    douyin = DouYin()
    douyin.try_cookie_login()
    if douyin.check_login_status():
        print('cookie 有效')
    else:
        print('cookie失效，开始登录')
        douyin.log_with_message_code('18697998680','234')
        if douyin.check_login_status():
            print('登录成功,开始保存cookie')
            douyin.save_cookies()
        else:
            print('登录失败，请查看日志！！！')

