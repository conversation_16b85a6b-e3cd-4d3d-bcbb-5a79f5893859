from django.db import models
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.dispatch import receiver

User = get_user_model()  # 获取系统用户模型

class WechatUser(models.Model):
    """
    微信用户关联模型
    功能：存储微信用户与系统用户的绑定关系
    字段说明：
    - user: 与系统用户的一对一关联
    - openid: 微信生态唯一标识（必须唯一）
    - session_key: 微信会话密钥（用于解密数据）
    - unionid: 微信开放平台统一标识（可选）
    - last_login: 最后登录时间（自动更新）
    """
    user = models.OneToOneField(User,on_delete=models.CASCADE,related_name='wechat_user',verbose_name='系统用户')
    username = models.CharField(max_length=150, unique=True, db_index=True, verbose_name='用户账号', help_text="用户账号")
    openid = models.CharField(max_length=128,unique=True,verbose_name='微信OpenID')
    session_key = models.CharField(max_length=256,verbose_name='会话密钥')
    unionid = models.Char<PERSON>ield(max_length=128,null=True,blank=True,verbose_name='UnionID')
    last_login = models.DateTimeField(auto_now=True,verbose_name='最后登录时间')

    class Meta:
        verbose_name = '微信用户'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['openid']),  # 为openid创建索引加速查询
        ]

    def __str__(self):
        return f"{self.user.username}  - {self.openid}"




@receiver(post_save, sender=User)
def create_wechat_user(sender, instance, created, **kwargs):
    """
    用户模型保存后的信号处理
    功能：当新用户创建时，自动创建关联的微信用户占位
    """
    if created:
        WechatUser.objects.get_or_create(user=instance)