#!/usr/bin/env python
# -*- coding: utf-8 -*-
# time: 2022/7/21 14:01
# file: api.py
# author: 臧成龙
# QQ: 939589097

from typing import List


from ninja import Field, ModelSchema, Query, Router
from ninja.pagination import paginate

from spiders.models import WeiboHotSearch, TiktokHotSearch
from utils.fu_crud import (
    ImportSchema,
    create,
    delete,
    export_data,
    import_data,
    retrieve,
    update,
)
from utils.fu_ninja import FuFilters, MyPagination

router = Router()


# 设置过滤字段
class Filters(FuFilters):
    hot_title: str = Field(None, alias="hot_title")


# 设置请求接收字段
class WeiboHotSearchSchemaIn(ModelSchema):
    # remark: list[str]
    class Config:
        model = WeiboHotSearch
        model_fields = ['hot_title', 'hot_index']


# 设置响应字段
class WeiboHotSearchSchemaOut(ModelSchema):
    class Config:
        model = WeiboHotSearch
        model_fields = ['hot_title', 'hot_index']


# 创建微博热搜
@router.post("/weibo_hot_search", response=WeiboHotSearchSchemaOut)
def create_weibo_hot_search(request, data: WeiboHotSearchSchemaIn):
    weibo_hot_search = create(request, data, WeiboHotSearch)
    return weibo_hot_search

# 触发抖音热搜爬虫
@router.post("/tiktok_hot_search/crawl")
def crawl_tiktok_hot_search_api(request):
    from spiders.tasks import crawl_tiktok_hot_search
    task = crawl_tiktok_hot_search.delay()
    return {"task_id": task.id, "message": "抖音热搜爬虫任务已启动"}

# 设置抖音热搜过滤字段
class TiktokHotSearchFilters(FuFilters):
    hot_title: str = Field(None, alias="hot_title")
    hot_type: str = Field(None, alias="hot_type")

# 设置抖音热搜请求接收字段
class TiktokHotSearchSchemaIn(ModelSchema):
    class Config:
        model = TiktokHotSearch
        model_fields = ['hot_title', 'hot_index', 'hot_desc', 'hot_url', 'hot_type', 'hot_value']

# 设置抖音热搜响应字段
class TiktokHotSearchSchemaOut(ModelSchema):
    class Config:
        model = TiktokHotSearch
        model_fields = ['id', 'hot_title', 'hot_index', 'hot_desc', 'hot_url', 'hot_type', 'hot_value', 'create_datetime']

# 获取抖音热搜列表
@router.get("/tiktok_hot_list", response=List[TiktokHotSearchSchemaOut])
@paginate(MyPagination)
def tiktok_hot_list(request, filters: TiktokHotSearchFilters = Query(...)):
    qs = retrieve(request, TiktokHotSearch, filters)
    return qs

# 获取热点
@router.get("/weibo_hot_list", response=List[WeiboHotSearchSchemaOut])
@paginate(MyPagination)
def weibo_hot_list(request, filters: Filters = Query(...)):
    qs = retrieve(request, WeiboHotSearch, filters)
    return qs
