import logging
import requests
from celery import shared_task
from spiders.models import WeiboHotSearch, ShortPlay

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@shared_task
def get_weibo_hot():
    # 设置模拟浏览器的 headers
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "DNT": "1",  # Do Not Track
    }
    url = 'https://weibo.com/ajax/side/hotSearch'

    try:
        response = requests.get(url,headers=headers)
        response.raise_for_status()  # Raises HTTPError for bad responses (4xx, 5xx)

        if response.status_code == 200:
            try:
                hot_json = response.json()  # This is better than json.load(response)
                if 'data' in hot_json:
                    _realtimes = hot_json.get('data', {})
                    realtimes = _realtimes.get('realtime', [])
                    if not realtimes:
                        logger.warning("没有热搜数据")
                    else:
                        for realtime in realtimes:
                            WeiboHotSearch.objects.create(
                                hot_title=realtime.get('word',''),
                                hot_index=realtime.get('rank',0),
                                label_name=realtime.get('label_name',''),
                                num=realtime.get('num',0),
                                flag_desc=realtime.get('flag_desc',''),
                                word_scheme=realtime.get('word_scheme',''),
                            )
                            logger.info(f"已保存热搜词: {realtime.get('word')}")
                else:
                    logger.warning("没有热搜数据")
            except (ValueError, KeyError) as e:
                logger.error(f"解析JSON失败: {e}")
        else:
            logger.error(f"请求失败，状态码: {response.status_code}")

    except requests.RequestException as e:
        logger.error(f"请求出错: {e}")

