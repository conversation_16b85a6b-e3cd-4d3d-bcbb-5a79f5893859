import logging
import re
import time
from DrissionPage import SessionPage, errors
from DrissionPage.errors import ElementNotFoundError, GetDocumentError
from celery import shared_task
from Stars.models import Star
from django.db.models import Max

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 获取数据库中最大ID
def get_max_index():
    # 使用 aggregate 获取最大 ID
    max_index = Star.objects.aggregate(max_id=Max('id'))['max_id']
    return max_index if max_index is not None else 0  # 如果数据库为空，则返回 0


@shared_task
def get_stars():
    err_index = 0
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "DNT": "1",  # Do Not Track
    }
    base_url = 'https://www.houyuanhui.com/mingxing/'

    page = SessionPage()

    # 获取数据库中的最大 ID 作为 index 起始值
    index = get_max_index() + 1  # 取最大 ID 加 1 作为新的索引

    while True:
        try:
            logger.info("开始抓取：" + base_url + str(index) + '/ziliao.html')
            page.get(base_url + str(index) + '/ziliao.html', headers=headers)
            message = None
            try:
                name = page.ele('@class=main')
                message = name.text
            except ElementNotFoundError:
                logger.warning("没有热搜数据")
                index += 1  # 错误时调整索引，继续下一页
                continue
            except GetDocumentError:
                logger.warning("没有热搜数据")
                index += 1  # 错误时调整索引，继续下一页
                continue
            except errors as e:
                logger.warning(f"发生错误: {e}")
                index += 1  # 错误时调整索引，继续下一页
                continue

            if message:
                pattern = r"(.+?)：(.+?)(?=\n|$)"
                matches = re.findall(pattern, str(message))
                key_mapping = {
                    '中文名': 'chinese_name',
                    '别名': 'aliases_name',
                    '外文名': 'foreign_name',
                    '国籍': 'country',
                    '星座': 'constellation',
                    '体重': 'weight',
                    '出生地': 'birth',
                    '经纪公司': 'a7',
                    '毕业院校': 'a8',
                    '民族': 'nation',
                    '血型': 'blood_group',
                    '身高': 'stature',
                    '出生日期': 'birthday',
                    '职业': 'profession'
                }
                star_data_modified = {key_mapping.get(k, k): v for k, v in matches}

                logger.info(f"成功抓取明星数据: {star_data_modified}")

                # 将抓取的数据存入数据库
                Star.objects.create(
                    id=index,
                    chinese_name=star_data_modified.get('chinese_name'),
                    aliases_name=star_data_modified.get('aliases_name', ''),
                    country=star_data_modified.get('country', ''),
                    constellation=star_data_modified.get('constellation', ''),
                    birth=star_data_modified.get('birth', ''),
                    nation=star_data_modified.get('nation', ''),
                    blood_group=star_data_modified.get('blood_group', ''),
                    stature=star_data_modified.get('stature', ''),
                    birthday=star_data_modified.get('birthday', ''),
                    profession=star_data_modified.get('profession', ''),
                    weight=star_data_modified.get('weight', ''),
                    foreign_name=star_data_modified.get('foreign_name', ''),
                )
                break
            else:
                logger.info(f"没有找到明星数据, index={index}")
                index += 1  # 没有数据也继续
        except Exception as e:
            logger.error(f"抓取过程中发生异常: {e}")
            index += 1  # 出现其他异常时，继续下一个明星
            err_index = err_index + 1
            if err_index == 10:
                logger.error("可能没有数据了，跳出去吧")
                break
            else:
                logger.info('错误次数' + str(err_index))
            time.sleep(2)  # 加入间隔，防止过于频繁的请求
