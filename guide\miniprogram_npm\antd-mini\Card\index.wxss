.ant-card {
  border-radius: 24rpx;
  background-color: var(--ant-card-bg, #ffffff);
}
.ant-card-header {
  display: flex;
  padding: 24rpx;
  justify-content: space-between;
}
.ant-card-header-title {
  color: var(--ant-card-header-title-color, #333333);
  line-height: 45rpx;
  font-size: 32rpx;
}
.ant-card-header-right-link {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--ant-card-header-title-color, #999999);
}
.ant-card-header:empty {
  margin-bottom: 0;
}
.ant-card-header-fold-btn {
  font-size: 26rpx;
  color: var(--ant-card-fold-btn-color, #1677ff);
}
.ant-card-header-fold-icon {
  font-size: 26rpx;
  margin-left: 8rpx;
  color: var(--ant-card-fold-btn-color, #1677ff);
}
.ant-card .ant-card-divider {
  margin: 0 24rpx;
}
.ant-card .ant-card-divider-with-line {
  position: relative;
}
.ant-card .ant-card-divider-with-line::before {
  content: '';
  position: absolute;
  background-color: var(--ant-card-divider-color, #eeeeee);
  display: block;
  top: 0;
  right: 0;
  bottom: auto;
  left: 0;
  height: 1px;
  transform: scaleY(0.5);
}
.ant-card .ant-card-content {
  padding: 24rpx;
  transition: all 0.2s ease-in-out;
  will-change: max-height;
}
.ant-card .ant-card-content-collapse {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}
