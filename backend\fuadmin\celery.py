# celery.py

from __future__ import absolute_import, unicode_literals

import os
import logging

from celery import Celery
from celery.schedules import crontab

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuadmin.settings')

# 检测环境
ENVIRONMENT = os.environ.get('DJANGO_ENV', 'production')
if ENVIRONMENT == 'test':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuadmin.settings_test')

app = Celery('fuadmin')  # Replace 'your_project' with your project's name.

# Configure Celery using settings from Django settings.py.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load tasks from all registered Django app configs.
app.autodiscover_tasks()
app.conf.imports=[
    # 'spiders.tk.task_line',
    # 'spiders.weibo',  # 添加微博爬虫任务
    'spiders.tasks',  # 添加抖音热搜爬虫任务
    'backend.test_celery_task',  # 使用绝对路径导入测试任务
]

# 配置celery日志
import os
from logging.handlers import RotatingFileHandler

# 确保logs目录存在
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

# Celery日志文件路径
CELERY_LOG_FILE = os.path.join(LOGS_DIR, 'celery.log')
CELERY_ERROR_LOG_FILE = os.path.join(LOGS_DIR, 'celery_error.log')

# 配置Celery根日志处理器
celery_logger = logging.getLogger('celery')
celery_logger.setLevel(logging.INFO)

# 清除已有的处理器
if celery_logger.handlers:
    celery_logger.handlers.clear()

# 添加文件处理器 - 普通日志
file_handler = RotatingFileHandler(
    CELERY_LOG_FILE,
    maxBytes=1024 * 1024 * 10,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
celery_logger.addHandler(file_handler)

# 添加文件处理器 - 错误日志
error_handler = RotatingFileHandler(
    CELERY_ERROR_LOG_FILE,
    maxBytes=1024 * 1024 * 10,  # 10MB
    backupCount=3,
    encoding='utf-8'
)
error_handler.setLevel(logging.ERROR)
error_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
celery_logger.addHandler(error_handler)

# 添加控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
celery_logger.addHandler(console_handler)

# 配置任务日志器
celery_task_logger = logging.getLogger('celery.task')
celery_task_logger.setLevel(logging.INFO)

# 清除已有的处理器
if celery_task_logger.handlers:
    celery_task_logger.handlers.clear()

# 添加相同的处理器到任务日志器
celery_task_logger.addHandler(file_handler)
celery_task_logger.addHandler(error_handler)
celery_task_logger.addHandler(console_handler)


# 定义定时任务
app.conf.beat_schedule = {
    # 微博热搜任务 - 每10分钟执行一次，用于ODS数据层存储
    'get_weibo_hot_task': {
        'task': 'spiders.weibo.get_weibo_hot',
        'schedule': crontab(minute='*/10'),  # 每10分钟执行
    },
    # 抖音热搜任务 - 每5分钟执行一次，用于ODS数据层存储
    'crawl_tiktok_hot_search_task': {
        'task': 'spiders.tasks.crawl_tiktok_hot_search',
        'schedule': crontab(minute='*/5'),  # 每5分钟执行
    },
    
}

# 配置任务结果存储
app.conf.result_expires = 3600  # 1小时过期

# 配置任务重试
app.conf.task_acks_late = True
app.conf.task_reject_on_worker_lost = True
app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']