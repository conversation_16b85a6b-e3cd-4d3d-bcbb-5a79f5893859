var esES = {
    // locales for all components
    locale: 'es-ES',
    global: {
        placeholder: 'Por favor, seleccione',
        emptyText: 'No hay datos disponibles',
        okText: 'Confirmar',
        cancelText: 'Cancelar',
    },
    input: {
        placeholder: 'Por favor, introduzca',
    },
    calendar: {
        weekdayNames: [
            'Lunes',
            'Martes',
            'Miércoles',
            'Jueves',
            'Viernes',
            'Sábado',
            'Domingo',
        ],
        format: 'MM/YYYY',
        today: 'Hoy',
        start: 'Empezar',
        end: '<PERSON>',
        startAndEnd: 'Inicio/Fin',
    },
    rangePicker: {
        startPlaceholder: 'No ha comenzado la selección',
        endPlaceholder: 'Selección no completada',
    },
    guideTour: {
        gotItText: 'Entendido',
        nextStepText: 'Siguiente paso',
        prevStepText: 'Paso anterior',
        jumpText: 'Saltar',
    },
    imageUpload: {
        uploadingText: 'Subiendo',
        uploadfailedText: 'Error al subir',
    },
    pageContainer: {
        failed: {
            title: 'La página ha encontrado algunos pequeños problemas',
            message: 'Lo probaré más tarde',
        },
        disconnected: {
            title: 'La red está un poco ocupada',
            message: 'Mueve tus dedos para ayudar a arreglarlo',
        },
        empty: {
            title: 'Aquí no hay nada',
            message: 'Echa un vistazo a los demás',
        },
        busy: {
            title: 'Congestión por delante',
            message: 'Intente refrescar',
        },
    },
};
export default esES;
