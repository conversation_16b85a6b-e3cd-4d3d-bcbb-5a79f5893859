from celery import shared_task
from fuadmin.celery import app
import time
import logging

from spiders.douyin.random_comments.random_comments import RandomComments
from spiders.tk.download_tk.tiktokDownloadManager import TiktokDownloadManager
from spiders.tk.temp.get_tiktok import TiktokDownloader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tiktok_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@shared_task()
@app.task(name='start_download')
def start_download():
    manager = TiktokDownloadManager()
    try:
        manager.process_downloads()
        time.sleep(60)  # 每分钟检查一次新任务
    except KeyboardInterrupt:
        manager.downloader.cleanup()
        logger.info("程序已安全退出")



@shared_task
@app.task(name='get_tiktok')
def get_tiktok():
    tiktokDownloader = TiktokDownloader()
    tiktokDownloader.get_videos_url()

@shared_task
@app.task(name='random_comment_on_douyin')
def random_comment_on_douyin():
    """评论类"""
    random_comments = RandomComments()
    # """cookie登录"""
    # random_comments.try_cookie_login()
    """初始化"""
    if random_comments.init_page():
        random_comments.random_comment()
    else:
        print("初始化页面失败并在十秒后关闭浏览器，请排查！！！")
        time.sleep(10)
        random_comments.quit()