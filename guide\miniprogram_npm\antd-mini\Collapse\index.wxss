.ant-collapse-item-disabled .ant-collapse-item-title-node,
.ant-collapse-item-disabled .ant-collapse-item-brief-container {
  opacity: 0.4;
}
.ant-collapse-item-disabled .ant-collapse-item-title:active {
  background: var(--collapse-title-background-color, #ffffff);
  transition: 0s;
}
.ant-collapse-item-line {
  display: flex;
  flex: 1;
  border-bottom: 1px solid var(--collapse-border-color, #e5e5e5);
  padding: 24rpx;
}
.ant-collapse-item-title {
  position: relative;
  display: flex;
  text-align: justify;
  align-items: center;
  justify-content: space-between;
  line-height: 48rpx;
  font-size: 34rpx;
  color: var(--collapse-title-color, #333333);
  background-color: var(--collapse-title-background-color, #ffffff);
  transition: all 300ms linear;
  box-sizing: border-box;
}
.ant-collapse-item-title-node {
  display: flex;
  flex: 1;
  max-width: 100%;
  font-size: 34rpx;
  color: var(--collapse-title-color, #333333);
}
.ant-collapse-item-title-arrow {
  color: var(--collapse-title-icon-color, #cccccc);
}
.ant-collapse-item-title-icon {
  width: 44rpx;
  height: 44rpx;
  overflow: hidden;
  margin-right: 24rpx;
}
.ant-collapse-item-title-icon .ant-icon {
  font-size: 40rpx;
}
.ant-collapse-item-title-icon image {
  width: 44rpx;
  height: 44rpx;
}
.ant-collapse-item-title:active {
  background-color: var(--collapse-border-color, #e5e5e5);
  transition: 0s;
}
.ant-collapse-item-brief-container {
  display: flex;
}
.ant-collapse-item-brief-container .ant-icon {
  font-size: 40rpx;
}
.ant-collapse-item-brief-node {
  display: flex;
  flex: 1;
  font-size: 30rpx;
  color: var(--collapse-node-text-color, #999999);
  margin-right: 8rpx;
}
.ant-collapse-item-content {
  color: var(--collapse-title-color, #333333);
  border-bottom: 1px solid var(--collapse-border-color, #e5e5e5);
  box-sizing: border-box;
  padding: 24rpx;
}
.ant-collapse-item-content-container {
  background: var(--collapse-container-background-color, #ffffff);
}
.ant-collapse-item-content-wrap {
  will-change: height;
  overflow: hidden;
}
.ant-collapse-item-content-wrap-active {
  animation: trigger1 0.2s;
}
.ant-collapse-item-content-wrap-non-active {
  animation: trigger2 0.2s;
}
.ant-collapse-item-content-wrap-transition {
  transition: height 0.2s ease-in-out;
}
.ant-collapse-item-content-wrap-first {
  height: 0;
}
@keyframes trigger1 {
  0% {
    content: '';
  }
  100% {
    content: '';
  }
}
@keyframes trigger2 {
  0% {
    content: '';
  }
  100% {
    content: '';
  }
}
