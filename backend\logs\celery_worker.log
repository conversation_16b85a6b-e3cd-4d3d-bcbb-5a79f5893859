[2025-08-05 22:02:03,615: WARNING/MainProcess] C:\Users\<USER>\.conda\envs\backend_cursor\Lib\site-packages\celery\worker\consumer\consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

[2025-08-05 22:02:03,622: INFO/MainProcess] Connected to redis://:**@127.0.0.1:6366/2
[2025-08-05 22:02:03,622: WARNING/MainProcess] C:\Users\<USER>\.conda\envs\backend_cursor\Lib\site-packages\celery\worker\consumer\consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

[2025-08-05 22:02:03,628: INFO/MainProcess] mingle: searching for neighbors
[2025-08-05 22:02:03,985: INFO/SpawnPoolWorker-2] child process 30196 calling self.run()
[2025-08-05 22:02:03,985: INFO/SpawnPoolWorker-1] child process 23616 calling self.run()
[2025-08-05 22:02:04,660: INFO/MainProcess] mingle: all alone
[2025-08-05 22:02:04,691: INFO/MainProcess] celery@joker ready.
