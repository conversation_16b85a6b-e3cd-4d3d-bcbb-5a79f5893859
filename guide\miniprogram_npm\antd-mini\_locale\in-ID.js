var inID = {
    // locales for all components
    locale: 'in-ID',
    global: {
        placeholder: 'Silakan pilih',
        emptyText: 'Tidak ada data',
        okText: 'Tentu',
        cancelText: 'Pembatalan',
    },
    input: {
        placeholder: 'Silakan masukkan',
    },
    calendar: {
        weekdayNames: [
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            'Sabtu',
            '<PERSON><PERSON>',
        ],
        format: 'MM/YYYY',
        today: 'Hari ini',
        start: '<PERSON><PERSON>',
        end: 'Akhir',
        startAndEnd: '<PERSON><PERSON>/Akhir',
    },
    rangePicker: {
        startPlaceholder: 'Seleksi belum dimulai',
        endPlaceholder: '<PERSON>le<PERSON>i belum selesai',
    },
    guideTour: {
        gotItText: 'Aku tahu',
        nextStepText: 'Langkah selanjutnya',
        prevStepText: '<PERSON><PERSON>h sebelumnya',
        jumpText: '<PERSON><PERSON>',
    },
    imageUpload: {
        uploadingText: 'Sedang diunggah',
        uploadfailedText: 'Gagal mengunggah',
    },
    pageContainer: {
        failed: {
            title: '<PERSON>aman ini mengalami beberapa masalah kecil',
            message: '<PERSON><PERSON> akan mencobanya nanti.',
        },
        disconnected: {
            title: 'Jaringannya agak sibuk',
            message: 'Gerakkan jari Anda untuk membantu memperbaikinya',
        },
        empty: {
            title: 'Tidak ada apa-apa di sini.',
            message: 'Lihat yang lain.',
        },
        busy: {
            title: 'Kemacetan di depan',
            message: 'Cobalah menyegarkan',
        },
    },
};
export default inID;
