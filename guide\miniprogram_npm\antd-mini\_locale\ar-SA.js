//ar-SA 阿拉伯语
var arSA = {
    // locales for all components
    locale: 'ar-SA',
    global: {
        placeholder: 'يرجى الاختيار',
        emptyText: 'لا توجد بيانات حتى الآن',
        okText: 'تأكيد',
        cancelText: 'إلغاء',
    },
    input: {
        placeholder: 'الرجاء إدخال',
    },
    calendar: {
        weekdayNames: [
            'الإثنين',
            'الثلاثاء',
            'الأربعاء',
            'الخميس',
            'الجمعة',
            'السبت',
            'الأحد',
        ],
        today: 'اليوم',
        start: 'ابدأ',
        end: 'النهاية',
        startAndEnd: 'البداية/النهاية',
        format: 'MM/YYYY',
    },
    rangePicker: {
        startPlaceholder: 'لم يبدأ الاختيار',
        endPlaceholder: 'لم يكتمل الاختيار',
    },
    guideTour: {
        gotItText: 'فهمت ذلك',
        nextStepText: 'الخطوة التالية',
        prevStepText: 'الخطوة السابقة',
        jumpText: 'تخطي',
    },
    imageUpload: {
        uploadingText: 'جارٍ التحميل',
        uploadfailedText: 'فشل في التحميل',
    },
    pageContainer: {
        failed: {
            title: 'تواجه الصفحة بعض المشكلات البسيطة',
            message: 'سأحاول ذلك لاحقًا',
        },
        disconnected: {
            title: 'الشبكة مشغولة بعض الشيء',
            message: 'حرّك إصبعك للمساعدة في الإصلاح',
        },
        empty: {
            title: 'لا يوجد شيء هنا',
            message: 'انظر إلى أشياء أخرى',
        },
        busy: {
            title: 'الازدحام الأمامي',
            message: 'جرّب الانتعاش.',
        },
    },
};
export default arSA;
