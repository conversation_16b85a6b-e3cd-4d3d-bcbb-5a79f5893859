component_to_db_type = {
    'Input': 'Char<PERSON><PERSON>(null=True, blank=True, max_length=255, ',
    'IconPicker': 'Char<PERSON><PERSON>(null=True, blank=True, max_length=255, ',
    'StrengthMeter': 'Char<PERSON><PERSON>(null=True, blank=True, max_length=255, ',
    'Select': 'Char<PERSON><PERSON>(null=True, blank=True, max_length=255, ',
    'CheckboxGroup': 'Char<PERSON><PERSON>(null=True, blank=True, max_length=255, ',
    'InputNumber': 'DecimalField(null=True, blank=True, max_digits = 13, decimal_places = 4, ',
    'InputTextArea': 'TextField(null=True, blank=True, ',
    'RadioGroup': 'Char<PERSON>ield(null=True, blank=True, max_length=255, ',
    'DatePicker': 'DateField(null=True, blank=True, ',
    'MonthPicker': 'DateField(null=True, blank=True, ',
    'TimePicker': 'TimeField(null=True, blank=True, ',
    'Switch': '<PERSON><PERSON><PERSON><PERSON><PERSON>(null=True, blank=True, ',
    'Rate': 'Char<PERSON><PERSON>(null=True, blank=True, max_length=255, ',
}

component_to_search_type = {
    'Input': 'str',
    'IconPicker': 'str',
    'StrengthMeter': 'str',
    'Select': 'str',
    'CheckboxGroup': 'str',
    'InputNumber': 'int',
    'InputTextArea': 'str',
    'RadioGroup': 'str',
    'DatePicker': 'str',
    'MonthPicker': 'str',
    'TimePicker': 'str',
    'Switch': 'bool',
    'Rate': 'str',
}