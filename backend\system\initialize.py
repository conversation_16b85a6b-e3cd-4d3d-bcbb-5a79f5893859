# 初始化
import datetime
import os

import django

from system.models import De<PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON><PERSON><PERSON>, <PERSON>, Users, Dict, DictItem
from utils.core_initialize import CoreInitialize

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()


class Initialize(CoreInitialize):
    creator_id = 1

    def init_dept(self):
        """
        初始化部门信息
        """
        self.dept_data = [
            {
                "id": 1,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "remark": None,
                "name": "北京公司",
                "sort": 1,
                "owner": None,
                "phone": "13244724433",
                "email": "<EMAIL>",
                "status": 1,
            },
            {
                "id": 2,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "remark": None,
                "name": "大连公司",
                "sort": 2,
                "owner": None,
                "phone": "13244724433",
                "email": "<EMAIL>",
                "status": 1,
            },
            {
                "id": 3,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "remark": None,
                "name": "IT部门",
                "sort": 1,
                "owner": None,
                "phone": "13244724433",
                "email": "<EMAIL>",
                "status": 1,
            },
            {
                "id": 4,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "remark": None,
                "name": "财务部门",
                "sort": 2,
                "owner": None,
                "phone": "13244724433",
                "email": "<EMAIL>",
                "status": 1,
            },
            {
                "id": 5,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 2,
                "remark": None,
                "name": "IT部门",
                "sort": 1,
                "owner": None,
                "phone": "13244724433",
                "email": "<EMAIL>",
                "status": 1,
            },
            {
                "id": 6,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 2,
                "remark": None,
                "name": "财务部门",
                "sort": 2,
                "owner": None,
                "phone": "13244724433",
                "email": "<EMAIL>",
                "status": 1,
            }
        ]
        self.save(Dept, self.dept_data, "部门信息")

    def init_menu(self):
        """
        初始化菜单表
        """
        self.menu_data = [
            {
                "id": 1,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 2,
                "icon": "ion:settings-outline",
                "title": "系统管理",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/system",
                "redirect": "/system/account",
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 2,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 3,
                "icon": "ant-design:appstore-outlined",
                "title": "系统工具",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/tool",
                "redirect": None,
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 3,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 4,
                "icon": "ant-design:code-filled",
                "title": "日志管理",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/log",
                "redirect": None,
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 4,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "sort": 1,
                "icon": "ant-design:bars-outlined",
                "title": "菜单管理",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "menu",
                "redirect": None,
                "component": "/fuadmin/system/menu/index",
                "name": "MenuManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 5,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "sort": 2,
                "icon": "ant-design:contacts-outlined",
                "title": "岗位管理",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "post",
                "redirect": None,
                "component": "/fuadmin/system/post/index",
                "name": "PostManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 6,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "sort": 3,
                "icon": "ant-design:user-outlined",
                "title": "用户管理",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "account",
                "redirect": None,
                "component": "/fuadmin/system/account/index",
                "name": "AccountManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 7,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "sort": 4,
                "icon": "ant-design:project-outlined",
                "title": "部门管理",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "dept",
                "redirect": None,
                "component": "/fuadmin/system/dept/index",
                "name": "DeptManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 8,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "sort": 5,
                "icon": "ant-design:team-outlined",
                "title": "角色管理",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "role",
                "redirect": None,
                "component": "/fuadmin/system/role/index",
                "name": "RoleManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 9,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 2,
                "sort": 1,
                "icon": "ant-design:database-outlined",
                "title": "数据字典",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "dict",
                "redirect": None,
                "component": "/fuadmin/system/data-dict/index",
                "name": "DataDict",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 10,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 2,
                "sort": 2,
                "icon": "ant-design:database-filled",
                "title": "分类字典",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "category",
                "redirect": None,
                "component": "/fuadmin/system/category_dict/index",
                "name": "CategoryDict",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 11,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 2,
                "sort": 3,
                "icon": "ant-design:clock-circle-outlined",
                "title": "定时任务",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "celery",
                "redirect": None,
                "component": "/fuadmin/system/celery/index",
                "name": "Celery",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 12,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 2,
                "sort": 4,
                "icon": "ant-design:folder-open-outlined",
                "title": "文件管理",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "file",
                "redirect": None,
                "component": "/fuadmin/system/file/index",
                "name": "FileManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 13,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 3,
                "sort": 1,
                "icon": "ant-design:book-outlined",
                "title": "登录日志",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "login_log",
                "redirect": None,
                "component": "/fuadmin/system/log/login_log/index",
                "name": "LoginLogManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 14,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 3,
                "sort": 2,
                "icon": "ant-design:book-outlined",
                "title": "操作日志",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "operation_log",
                "redirect": None,
                "component": "/fuadmin/system/log/operation_log/index",
                "name": "OperationLogManagement",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 15,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 3,
                "sort": 3,
                "icon": "ant-design:book-outlined",
                "title": "任务日志",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "celery_log",
                "redirect": None,
                "component": "/fuadmin/system/log/celery_log/index",
                "name": "CeleryLog",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 16,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 1,
                "icon": "bx:bx-home",
                "title": "Dashboard",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/dashboard",
                "redirect": "/dashboard/analysis",
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 17,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 16,
                "sort": 1,
                "icon": "bx:bx-home",
                "title": "分析页",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "analysis",
                "redirect": None,
                "component": "/dashboard/analysis/index",
                "name": "Analysis",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 18,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 16,
                "sort": 2,
                "icon": "bx:bx-home",
                "title": "工作台",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "workbench",
                "redirect": None,
                "component": "/dashboard/workbench/index",
                "name": "Workbench",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 19,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 1,
                "sort": 6,
                "icon": "ant-design:setting-outlined",
                "title": "个人设置",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "account/setting/:id",
                "redirect": None,
                "component": "/fuadmin/system/account/setting/index",
                "name": "AccountSetting",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 1,
            },
            {
                "id": 20,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 5,
                "icon": "ant-design:bar-chart-outlined",
                "title": "图表演示",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/chart",
                "redirect": "/chart/amap",
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 21,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 20,
                "sort": 1,
                "icon": "ant-design:environment-outlined",
                "title": "百度地图",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "bmap",
                "redirect": None,
                "component": "/fuadmin/charts/map/Baidu",
                "name": "BaiduMap",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 22,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 20,
                "sort": 2,
                "icon": "ant-design:environment-outlined",
                "title": "高德地图",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "amap",
                "redirect": None,
                "component": "/fuadmin/charts/map/Gaode",
                "name": "AMap",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 23,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 20,
                "sort": 3,
                "icon": "ant-design:bar-chart-outlined",
                "title": "Echart Map",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "echartmap",
                "redirect": None,
                "component": "/fuadmin/charts/Map",
                "name": "AMap",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 24,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 7,
                "icon": "ant-design:audit-outlined",
                "title": "项目演示",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "/demo",
                "redirect": None,
                "component": "/demo/index",
                "name": "Demo",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 25,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 6,
                "icon": "ant-design:fund-projection-screen-outlined",
                "title": "运维管理",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/operation",
                "redirect": "/chart/amap",
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 26,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 25,
                "sort": 1,
                "icon": "ant-design:monitor-outlined",
                "title": "服务监控",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "monitor",
                "redirect": None,
                "component": "/fuadmin/system/monitor/server",
                "name": "Server",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 29,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 8,
                "icon": "ant-design:border-outlined",
                "title": "代码生成",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/code-generator",
                "redirect": "/code-generator/generator-template",
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 30,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": 29,
                "sort": 1,
                "icon": "ant-design:book-outlined",
                "title": "代码模板",
                "permission": None,
                "is_ext": 0,
                "type": 1,
                "path": "generator-template",
                "redirect": None,
                "component": "/fuadmin/system/code-generator/template/index",
                "name": "generatorTemplate",
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
            {
                "id": 33,
                "modifier": "超级管理员",
                "belong_dept": None,
                "creator_id": 1,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "parent_id": None,
                "sort": 8,
                "icon": "ant-design:one-to-one-outlined",
                "title": "生成项目",
                "permission": None,
                "is_ext": 0,
                "type": 0,
                "path": "/generator_project",
                "redirect": None,
                "component": "LAYOUT",
                "name": None,
                "status": 1,
                "keepalive": 0,
                "hide_menu": 0,
            },
        ]
        self.save(Menu, self.menu_data, "菜单表")

    def init_menu_button(self):
        """
        初始化菜单按钮表
        """
        self.menu_button_data = [
            {
                "id": 1,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "post:add",
                "api": "/api/system/post",
                "method": 1,
                "creator_id": 1,
                "menu_id": 5,
                "belong_dept": None
            },
            {
                "id": 2,
                "remark": "",
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "post:delete",
                "api": "/api/system/post/{post_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 5,
                "belong_dept": None
            },
            {
                "id": 3,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "编辑",
                "code": "post:update",
                "api": "/api/system/post/{post_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 5,
                "belong_dept": None
            },
            {
                "id": 4,
                "remark": "",
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "post:search",
                "api": "/api/system/post",
                "method": 0,
                "creator_id": 1,
                "menu_id": 5,
                "belong_dept": None
            },
            {
                "id": 5,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "menu:add",
                "api": "/api/system/menu",
                "method": 1,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 6,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "menu:delete",
                "api": "/api/system/menu/{menu_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 7,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "编辑",
                "code": "menu:update",
                "api": "/api/system/menu/{menu_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 8,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "menu:search",
                "api": "/api/system/menu",
                "method": 0,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 9,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "按钮查询",
                "code": "button:add",
                "api": "/api/system/menu_button",
                "method": 0,
                "creator_id": 1,
                "menu_id": 8,
                "belong_dept": None
            },
            {
                "id": 10,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 7,
                "name": "按钮编辑",
                "code": "button:update",
                "api": "/api/system/menu_button/{menu_button_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 11,
                "remark": "",
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 5,
                "name": "按钮新增",
                "code": "button:add",
                "api": "/api/system/menu_button",
                "method": 1,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 12,
                "remark": "",
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 6,
                "name": "按钮删除",
                "code": "button:delete",
                "api": "/api/system/menu_button/{menu_button}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 4,
                "belong_dept": None
            },
            {
                "id": 13,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "user:add",
                "api": "/api/system/user",
                "method": 1,
                "creator_id": 1,
                "menu_id": 6,
                "belong_dept": None
            },
            {
                "id": 14,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "user:delete",
                "api": "/api/system/user/{user_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 6,
                "belong_dept": None
            },
            {
                "id": 15,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "修改",
                "code": "user:update",
                "api": "/api/system/user/{user_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 6,
                "belong_dept": None
            },
            {
                "id": 16,
                "remark": "",
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "user:search",
                "api": "/api/system/user",
                "method": 0,
                "creator_id": 1,
                "menu_id": 6,
                "belong_dept": None
            },
            {
                "id": 17,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "dept:add",
                "api": "/api/system/dept",
                "method": 1,
                "creator_id": 1,
                "menu_id": 7,
                "belong_dept": None
            },
            {
                "id": 18,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "dept:delete",
                "api": "/api/system/dept/{dept_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 7,
                "belong_dept": None
            },
            {
                "id": 19,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "修改",
                "code": "dept:update",
                "api": "/api/system/dept/{dept_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 7,
                "belong_dept": None
            },
            {
                "id": 20,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "dept:search",
                "api": "/api/system/dept",
                "method": 0,
                "creator_id": 1,
                "menu_id": 7,
                "belong_dept": None
            },
            {
                "id": 21,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "role:add",
                "api": "/api/system/role",
                "method": 1,
                "creator_id": 1,
                "menu_id": 8,
                "belong_dept": None
            },
            {
                "id": 22,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "role:delete",
                "api": "/api/system/role/{role_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 8,
                "belong_dept": None
            },
            {
                "id": 23,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "修改",
                "code": "role:update",
                "api": "/api/system/role/{role_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 8,
                "belong_dept": None
            },
            {
                "id": 24,
                "remark": "",
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "role:search",
                "api": "/api/system/role",
                "method": 0,
                "creator_id": 1,
                "menu_id": 8,
                "belong_dept": None
            },
            {
                "id": 25,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "dict:add",
                "api": "/api/dict/post",
                "method": 1,
                "creator_id": 1,
                "menu_id": 9,
                "belong_dept": None
            },
            {
                "id": 26,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "dict:delete",
                "api": "/api/system/dict/{dict_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 9,
                "belong_dept": None
            },
            {
                "id": 27,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "编辑",
                "code": "dict:update",
                "api": "/api/system/dict/{dict_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 9,
                "belong_dept": None
            },
            {
                "id": 28,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "dict:search",
                "api": "/api/system/dict",
                "method": 0,
                "creator_id": 1,
                "menu_id": 9,
                "belong_dept": None
            },
            {
                "id": 29,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "category:add",
                "api": "/api/system/category_dict",
                "method": 1,
                "creator_id": 1,
                "menu_id": 10,
                "belong_dept": None
            },
            {
                "id": 30,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "category:delete",
                "api": "/api/system/category_dict/{category_dict_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 10,
                "belong_dept": None
            },
            {
                "id": 31,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "name": "编辑",
                "code": "category:update",
                "api": "/api/system/category_dict/{category_dict_id}",
                "method": 2,
                "creator_id": 1,
                "menu_id": 10,
                "belong_dept": None
            },
            {
                "id": 32,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "category:search",
                "api": "/api/system/category_dict",
                "method": 0,
                "creator_id": 1,
                "menu_id": 10,
                "belong_dept": None
            },
            {
                "id": 33,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "新增",
                "code": "demo:add",
                "api": "/api/demo/demo",
                "method": 1,
                "creator_id": 1,
                "menu_id": 24,
                "belong_dept": None
            },
            {
                "id": 34,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "删除",
                "code": "demo:delete",
                "api": "/api/demo/demo/{demo_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 24,
                "belong_dept": None
            },
            {
                "id": 35,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "修改",
                "code": "demo:update",
                "api": "/api/demo/demo/{demo_id}",
                "method": 3,
                "creator_id": 1,
                "menu_id": 24,
                "belong_dept": None
            },
            {
                "id": 36,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 4,
                "name": "查询",
                "code": "demo:search",
                "api": "/api/demo/demo",
                "method": 0,
                "creator_id": 1,
                "menu_id": 24,
                "belong_dept": None
            },
        ]
        self.save(MenuButton, self.menu_button_data, "菜单按钮表")

    def init_role(self):
        """
        初始化角色表
        """
        data = [
            {
                "id": 1,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "行政人员",
                "code": "official",
                "status": 1,
                "admin": 0,
                "creator_id": 1,
                "belong_dept": None,
                "data_range": 4
            },
            {
                "id": 2,
                "remark": None,
                "modifier": "超级管理员",
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "name": "技术人员",
                "code": "technicist",
                "status": 1,
                "admin": 0,
                "creator_id": 1,
                "belong_dept": None,
                "data_range": 4
            },
        ]
        self.save(Role, data, "角色表")

    def init_dict(self):
        """
        初始化字典
        """
        data = [
            {
                "id": 1,
                "modifier": "超级管理员",
                "belong_dept": None,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "name": "项目状态",
                "code": "project_status",
                "status": 1,
                "remark": None,
                "creator_id": 1
            },
        ]
        self.save(Dict, data, "数据字典", no_reset=True)

    def init_dict_item(self):
        """
        初始化字典项目
        """
        data = [
            {
                "id": 1,
                "modifier": "超级管理员",
                "belong_dept": None,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 2,
                "label": "未建",
                "value": "未建",
                "status": 1,
                "remark": None,
                "creator_id": 1,
                "dict_id": 1
            },
            {
                "id": 2,
                "modifier": "超级管理员",
                "belong_dept": None,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "label": "在建",
                "value": "在建",
                "status": 1,
                "remark": None,
                "creator_id": 1,
                "dict_id": 1
            },
            {
                "id": 3,
                "modifier": "超级管理员",
                "belong_dept": None,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 3,
                "label": "竣工",
                "value": "竣工",
                "status": 1,
                "remark": None,
                "creator_id": 1,
                "dict_id": 1
            },
        ]
        self.save(DictItem, data, "字典项目", no_reset=True)

    def init_users(self):
        """
        初始化用户表
        """
        data = [
            {
                "password": "pbkdf2_sha256$320000$w6qJldmyHhHA2LJLROT5bx$Q/SDb4mvQ7qT5qYKV3naEFJqVHoq5yUSfSAiwssIEM8=",
                "last_login": None,
                "is_superuser": 1,
                "first_name": "",
                "last_name": "",
                "is_staff": 1,
                "is_active": 1,
                "date_joined": datetime.datetime.now(),
                "id": 1,
                "remark": None,
                "modifier": None,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "username": "superadmin",
                "email": "",
                "mobile": None,
                "name": "超级管理员",
                "gender": 1,
                "user_type": 0,
                "dept_id": None,
                "status": 1,
                "belong_dept": None,
                "avatar": None,
                "creator_id": None
            },
            {
                "password": "pbkdf2_sha256$320000$w6qJldmyHhHA2LJLROT5bx$Q/SDb4mvQ7qT5qYKV3naEFJqVHoq5yUSfSAiwssIEM8=",
                "last_login": None,
                "is_superuser": 0,
                "first_name": None,
                "last_name": None,
                "is_staff": 0,
                "is_active": 1,
                "date_joined": datetime.datetime.now(),
                "id": 4,
                "remark": None,
                "modifier": None,
                "update_datetime": datetime.datetime.now(),
                "create_datetime": datetime.datetime.now(),
                "sort": 1,
                "username": "test",
                "email": "<EMAIL>",
                "mobile": None,
                "name": "Test",
                "gender": 1,
                "user_type": 0,
                "dept_id": 2,
                "status": 1,
                "belong_dept": None,
                "avatar": None,
                "creator_id": None
            }
        ]
        self.save(Users, data, "用户表", no_reset=True)

    def run(self):
        self.init_dept()
        self.init_menu()
        self.init_menu_button()
        self.init_dict()
        self.init_dict_item()
        self.init_role()
        self.init_users()


# 项目init 初始化，默认会执行 main 方法进行初始化
def main(reset=False):
    Initialize(reset).run()
    pass


if __name__ == '__main__':
    main()
