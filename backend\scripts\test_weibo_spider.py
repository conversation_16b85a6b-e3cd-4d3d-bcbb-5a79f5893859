#!/usr/bin/env python
"""
微博爬虫测试脚本
用于测试微博热搜爬虫功能 - ODS数据层存储
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuadmin.settings_test')
django.setup()

from spiders.weibo import get_weibo_hot
from spiders.models import WeiboHotSearch
from datetime import datetime, timedelta

def test_weibo_spider():
    """
    测试微博爬虫功能 - ODS数据层存储
    """
    print("开始测试微博爬虫 - ODS数据层存储...")
    print(f"当前环境: {os.environ.get('DJANGO_ENV', 'production')}")
    print(f"数据库: {django.conf.settings.DATABASES['default']['NAME']}")
    print("-" * 50)
    
    try:
        # 执行爬虫任务
        result = get_weibo_hot()
        print(f"爬虫执行结果: {result}")
        
        # 查询最近1小时的数据
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent_records = WeiboHotSearch.objects.filter(
            create_datetime__gte=one_hour_ago
        ).order_by('-create_datetime')[:20]
        
        print(f"\n最近1小时热搜记录 (共 {recent_records.count()} 条):")
        print("-" * 50)
        
        # 按时间分组显示
        time_groups = {}
        for record in recent_records:
            time_key = record.create_datetime.strftime('%Y-%m-%d %H:%M')
            if time_key not in time_groups:
                time_groups[time_key] = []
            time_groups[time_key].append(record)
        
        for time_key in sorted(time_groups.keys(), reverse=True):
            records = time_groups[time_key]
            print(f"\n📅 {time_key} (共 {len(records)} 条记录):")
            print("-" * 30)
            
            for record in records[:5]:  # 每个时间点只显示前5条
                print(f"  排名: {record.hot_index:2d} | 标题: {record.hot_title}")
                print(f"  标签: {record.label_name} | 分类: {record.flag_desc}")
            
            if len(records) > 5:
                print(f"  ... 还有 {len(records) - 5} 条记录")
        
        # 统计信息
        total_today = WeiboHotSearch.objects.filter(
            create_datetime__date=datetime.now().date()
        ).count()
        
        print(f"\n📊 今日数据统计:")
        print(f"  今日总记录数: {total_today}")
        print(f"  最近1小时记录数: {recent_records.count()}")
        
        print("\n✅ 测试完成！")
        print("💡 提示: 这是ODS数据层存储，所有记录都会保存用于后续分析")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_weibo_spider() 