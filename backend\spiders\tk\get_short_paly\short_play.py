import requests
from celery import shared_task

from spiders.models import ShortPlay


@shared_task
def get_short_play():
    api_url = 'https://api.kuleu.com/api/action?text=' + '霸道'
    response = None
    try:
        response = requests.get(api_url)
        response.raise_for_status()
    except Exception as e:
        print(e)
        return

    if response.status_code == 200:
        try:
            message_json = response.json()
            if 'data' in message_json:
                short_plays = message_json.get('data',[])
                for short_play in short_plays:
                    ShortPlay.objects.create(
                        name = short_play.get('name',''),
                        quark_download_link = short_play.get('viewlink',''),
                        api_add_time = short_play.get('addtime',''),
                    )
            else:
                print(1)
        except Exception as e:
            print(e)
            return
