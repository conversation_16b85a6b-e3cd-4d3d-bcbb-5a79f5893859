from DrissionPage import ChromiumPage
from abc import ABC, abstractmethod
from typing import Dict, List


# -------------------- 工厂方法模式 + 建造者模式 --------------------
class BrowserConfig:
    """浏览器配置对象（建造者模式产物）"""

    def __init__(self):
        self.timeout = 10
        self.headless = False
        self.proxy = None
        self.user_agent = None


class BrowserBuilder:
    """配置建造者"""

    def __init__(self):
        self.config = BrowserConfig()

    def set_timeout(self, seconds: int):
        self.config.timeout = seconds
        return self

    def set_headless(self, enabled: bool):
        self.config.headless = enabled
        return self

    def set_proxy(self, proxy: str):
        self.config.proxy = proxy
        return self

    def set_user_agent(self, ua: str):
        self.config.user_agent = ua
        return self

    def build(self) -> BrowserConfig:
        return self.config


class BrowserFactory(ABC):
    """抽象工厂"""

    @abstractmethod
    def create_browser(self, config: BrowserConfig) -> ChromiumPage:
        pass


class ChromiumFactory(BrowserFactory):
    """具体工厂"""

    def create_browser(self, config: BrowserConfig) -> ChromiumPage:
        page = ChromiumPage()
        page.set.timeouts(config.timeout)
        if config.headless:
            page.set.headless(True)
        if config.proxy:
            page.set.proxy(config.proxy)
        if config.user_agent:
            page.set.user_agent(config.user_agent)
        return page


# -------------------- 组合模式（标签管理） --------------------
class TabComponent(ABC):
    """抽象组件"""

    @abstractmethod
    def activate(self):
        pass


class TabLeaf(TabComponent):
    """叶子节点（单个标签页）"""

    def __init__(self, tab):
        self._tab = tab

    def activate(self):
        self._tab.activate()


class TabManager(TabComponent):
    """组合节点（管理多个标签页）"""

    def __init__(self):
        self._tabs: Dict[str, TabComponent] = {}

    def add_tab(self, tab_id: str, tab: TabComponent):
        self._tabs[tab_id] = tab

    def remove_tab(self, tab_id: str):
        return self._tabs.pop(tab_id, None)

    def activate(self):
        print("Cannot activate tab manager")

    def get_tab(self, tab_id: str) -> TabComponent:
        return self._tabs.get(tab_id)

# --------------------...
