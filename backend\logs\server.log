[2025-08-05 21:57:06,066][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\fuadmin\celery.py changed, reloading.
[2025-08-05 21:57:07,400][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 21:58:43,920][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\fuadmin\settings.py changed, reloading.
[2025-08-05 21:58:45,047][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:01:50,887][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\fuadmin\celery.py changed, reloading.
[2025-08-05 22:01:52,127][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:09:44,166][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\Mywechatmini\api.py changed, reloading.
[2025-08-05 22:09:45,403][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:10:03,180][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\Mywechatmini\admin.py changed, reloading.
[2025-08-05 22:10:04,395][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:12:39,860][django.server.log_message():212] [INFO] "GET /api/system/userinfo?_t=1754403159089 HTTP/1.1" 200 652
[2025-08-05 22:12:40,506][django.server.log_message():212] [INFO] "GET /api/system/permCode?_t=1754403159874 HTTP/1.1" 200 599
[2025-08-05 22:12:41,219][django.server.log_message():212] [INFO] "GET /api/system/menu/route/tree?_t=1754403160509 HTTP/1.1" 200 15618
[2025-08-05 22:12:44,445][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754403163745 HTTP/1.1" 200 15096
[2025-08-05 22:12:47,008][django.server.log_message():212] [INFO] "GET /api/system/post?page=1&pageSize=10&_t=1754403166238 HTTP/1.1" 200 90
[2025-08-05 22:12:48,592][django.server.log_message():212] [INFO] "GET /api/system/dept/list/tree?_t=1754403167920 HTTP/1.1" 200 2209
[2025-08-05 22:12:49,095][django.server.log_message():212] [INFO] "GET /api/system/dept/list/tree?_t=1754403168513 HTTP/1.1" 200 2209
[2025-08-05 22:12:49,104][django.server.log_message():212] [INFO] "GET /api/system/user?page=1&pageSize=10&_t=1754403167991 HTTP/1.1" 200 1264
[2025-08-05 22:12:50,364][django.server.log_message():212] [INFO] "GET /api/system/role?page=1&pageSize=10&_t=1754403169360 HTTP/1.1" 200 834
[2025-08-05 22:12:58,074][django.server.log_message():212] [INFO] "GET /api/demo/demo?page=1&pageSize=10&_t=1754403177481 HTTP/1.1" 200 90
[2025-08-05 22:13:02,546][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403180694 HTTP/1.1" 200 1772
[2025-08-05 22:13:04,510][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403183719 HTTP/1.1" 200 1762
[2025-08-05 22:13:07,437][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403186721 HTTP/1.1" 200 1762
[2025-08-05 22:13:10,431][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403189729 HTTP/1.1" 200 1760
[2025-08-05 22:13:13,448][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403192719 HTTP/1.1" 200 1762
[2025-08-05 22:13:16,410][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403195718 HTTP/1.1" 200 1762
[2025-08-05 22:13:19,418][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403198726 HTTP/1.1" 200 1762
[2025-08-05 22:13:23,955][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403202287 HTTP/1.1" 200 1765
[2025-08-05 22:13:26,082][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403205288 HTTP/1.1" 200 1766
[2025-08-05 22:13:29,065][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403208277 HTTP/1.1" 200 1765
[2025-08-05 22:13:32,058][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403211290 HTTP/1.1" 200 1761
[2025-08-05 22:13:34,989][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403214275 HTTP/1.1" 200 1762
[2025-08-05 22:13:38,020][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403217276 HTTP/1.1" 200 1764
[2025-08-05 22:13:41,061][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403220287 HTTP/1.1" 200 1764
[2025-08-05 22:13:44,062][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403223285 HTTP/1.1" 200 1766
[2025-08-05 22:13:47,060][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403226277 HTTP/1.1" 200 1764
[2025-08-05 22:13:49,948][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403229281 HTTP/1.1" 200 1763
[2025-08-05 22:13:53,045][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403232278 HTTP/1.1" 200 1767
[2025-08-05 22:13:55,972][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403235285 HTTP/1.1" 200 1767
[2025-08-05 22:13:58,935][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403238277 HTTP/1.1" 200 1762
[2025-08-05 22:14:02,072][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403241285 HTTP/1.1" 200 1762
[2025-08-05 22:14:04,977][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403244281 HTTP/1.1" 200 1762
[2025-08-05 22:14:08,056][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403247281 HTTP/1.1" 200 1764
[2025-08-05 22:14:11,034][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403250288 HTTP/1.1" 200 1766
[2025-08-05 22:14:14,007][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403253282 HTTP/1.1" 200 1762
[2025-08-05 22:14:17,061][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403256276 HTTP/1.1" 200 1764
[2025-08-05 22:14:19,942][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403259282 HTTP/1.1" 200 1762
[2025-08-05 22:14:27,995][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403267281 HTTP/1.1" 200 1759
[2025-08-05 22:15:29,060][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403327289 HTTP/1.1" 200 1765
[2025-08-05 22:16:29,072][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403387287 HTTP/1.1" 200 1765
[2025-08-05 22:17:29,009][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403447280 HTTP/1.1" 200 1767
[2025-08-05 22:18:19,525][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403497789 HTTP/1.1" 200 1763
[2025-08-05 22:18:19,525][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403498724 HTTP/1.1" 200 1772
[2025-08-05 22:18:23,005][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403502289 HTTP/1.1" 200 1765
[2025-08-05 22:18:26,026][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403505281 HTTP/1.1" 200 1767
[2025-08-05 22:18:29,045][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403508290 HTTP/1.1" 200 1766
[2025-08-05 22:18:32,023][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403511288 HTTP/1.1" 200 1762
[2025-08-05 22:18:34,948][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403514282 HTTP/1.1" 200 1763
[2025-08-05 22:18:38,076][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403517287 HTTP/1.1" 200 1761
[2025-08-05 22:18:40,957][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403520283 HTTP/1.1" 200 1763
[2025-08-05 22:18:44,001][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403523276 HTTP/1.1" 200 1762
[2025-08-05 22:18:47,041][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403526288 HTTP/1.1" 200 1761
[2025-08-05 22:18:49,960][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403529277 HTTP/1.1" 200 1764
[2025-08-05 22:18:53,013][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403532281 HTTP/1.1" 200 1762
[2025-08-05 22:18:55,964][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403535276 HTTP/1.1" 200 1761
[2025-08-05 22:18:59,018][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403538280 HTTP/1.1" 200 1761
[2025-08-05 22:19:02,118][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403541280 HTTP/1.1" 200 1764
[2025-08-05 22:19:05,038][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403544283 HTTP/1.1" 200 1762
[2025-08-05 22:19:08,077][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403547275 HTTP/1.1" 200 1761
[2025-08-05 22:19:11,035][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403550283 HTTP/1.1" 200 1768
[2025-08-05 22:19:14,089][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403553280 HTTP/1.1" 200 1766
[2025-08-05 22:19:16,994][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403556289 HTTP/1.1" 200 1764
[2025-08-05 22:19:27,965][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403567275 HTTP/1.1" 200 1775
[2025-08-05 22:20:28,997][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403627277 HTTP/1.1" 200 1767
[2025-08-05 22:21:29,006][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403687288 HTTP/1.1" 200 1767
[2025-08-05 22:22:29,061][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403747287 HTTP/1.1" 200 1766
[2025-08-05 22:23:29,047][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403807289 HTTP/1.1" 200 1766
[2025-08-05 22:24:29,031][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403867286 HTTP/1.1" 200 1762
[2025-08-05 22:25:29,084][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403927280 HTTP/1.1" 200 1764
[2025-08-05 22:26:29,035][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403987282 HTTP/1.1" 200 1765
[2025-08-05 22:27:29,030][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404047282 HTTP/1.1" 200 1764
[2025-08-05 22:28:28,985][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404107281 HTTP/1.1" 200 1778
[2025-08-05 22:29:29,119][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404167280 HTTP/1.1" 200 1765
[2025-08-05 22:30:28,963][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404227280 HTTP/1.1" 200 1767
[2025-08-05 22:31:29,006][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404287283 HTTP/1.1" 200 1769
[2025-08-05 22:32:29,043][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404347287 HTTP/1.1" 200 1764
[2025-08-05 22:33:29,118][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404407286 HTTP/1.1" 200 1764
[2025-08-05 22:34:29,068][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404467286 HTTP/1.1" 200 1762
[2025-08-05 22:35:29,184][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404527283 HTTP/1.1" 200 1765
[2025-08-05 22:36:28,986][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404587275 HTTP/1.1" 200 1764
[2025-08-05 22:37:29,051][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404647282 HTTP/1.1" 200 1765
[2025-08-05 22:38:28,960][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404707277 HTTP/1.1" 200 1762
[2025-08-05 22:39:29,032][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404767277 HTTP/1.1" 200 1769
[2025-08-05 22:40:29,062][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404827290 HTTP/1.1" 200 1765
[2025-08-05 22:41:29,062][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404887288 HTTP/1.1" 200 1761
[2025-08-05 22:42:29,053][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404947282 HTTP/1.1" 200 1761
[2025-08-05 22:43:29,001][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405007290 HTTP/1.1" 200 1764
[2025-08-05 22:44:29,176][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405067284 HTTP/1.1" 200 1763
[2025-08-05 22:45:29,088][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405127284 HTTP/1.1" 200 1762
[2025-08-05 22:46:28,981][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405187288 HTTP/1.1" 200 1762
[2025-08-05 22:47:28,964][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405247278 HTTP/1.1" 200 1772
[2025-08-05 22:48:28,988][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405307284 HTTP/1.1" 200 1768
[2025-08-05 22:49:28,960][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405367284 HTTP/1.1" 200 1763
[2025-08-05 22:50:28,999][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405427286 HTTP/1.1" 200 1767
[2025-08-05 22:51:29,080][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405487281 HTTP/1.1" 200 1762
[2025-08-05 22:52:29,062][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405547286 HTTP/1.1" 200 1762
[2025-08-05 22:53:29,101][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405607288 HTTP/1.1" 200 1763
[2025-08-05 22:54:29,035][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405667290 HTTP/1.1" 200 1767
[2025-08-05 22:55:29,116][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405727286 HTTP/1.1" 200 1762
[2025-08-05 22:56:29,034][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405787278 HTTP/1.1" 200 1763
[2025-08-05 22:57:28,963][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405847287 HTTP/1.1" 200 1763
[2025-08-05 22:58:29,077][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405907285 HTTP/1.1" 200 1765
[2025-08-05 22:59:29,026][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754405967275 HTTP/1.1" 200 1763
[2025-08-05 23:00:28,992][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406027281 HTTP/1.1" 200 1763
[2025-08-05 23:01:29,040][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406087281 HTTP/1.1" 200 1766
[2025-08-05 23:02:28,976][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406147277 HTTP/1.1" 200 1762
[2025-08-05 23:03:59,108][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 23:06:29,037][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406387279 HTTP/1.1" 200 1763
[2025-08-05 23:07:29,001][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406447277 HTTP/1.1" 200 1763
[2025-08-05 23:08:28,965][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406507287 HTTP/1.1" 200 1764
[2025-08-05 23:09:29,004][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406567286 HTTP/1.1" 200 1763
[2025-08-05 23:10:29,026][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406627283 HTTP/1.1" 200 1763
[2025-08-05 23:11:28,961][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406687285 HTTP/1.1" 200 1762
[2025-08-05 23:12:29,005][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406747285 HTTP/1.1" 200 1765
[2025-08-05 23:13:29,052][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406807280 HTTP/1.1" 200 1762
[2025-08-05 23:14:29,030][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406867289 HTTP/1.1" 200 1763
[2025-08-05 23:15:29,091][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406927288 HTTP/1.1" 200 1763
[2025-08-05 23:16:29,053][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754406987276 HTTP/1.1" 200 1765
[2025-08-05 23:17:29,023][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407047290 HTTP/1.1" 200 1760
[2025-08-05 23:18:29,088][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407107278 HTTP/1.1" 200 1766
[2025-08-05 23:19:29,025][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407167276 HTTP/1.1" 200 1761
[2025-08-05 23:20:28,976][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407227287 HTTP/1.1" 200 1764
[2025-08-05 23:21:28,990][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407287289 HTTP/1.1" 200 1761
[2025-08-05 23:21:32,427][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407291650 HTTP/1.1" 200 1768
[2025-08-05 23:21:47,022][django.server.log_message():212] [INFO] "GET /api/system/userinfo?_t=1754407306498 HTTP/1.1" 200 652
[2025-08-05 23:21:47,564][django.server.log_message():212] [INFO] "GET /api/system/permCode?_t=1754407307050 HTTP/1.1" 200 599
[2025-08-05 23:21:48,198][django.server.log_message():212] [INFO] "GET /api/system/menu/route/tree?_t=1754407307566 HTTP/1.1" 200 15618
[2025-08-05 23:21:52,402][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407310379 HTTP/1.1" 200 1766
[2025-08-05 23:21:54,984][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407314280 HTTP/1.1" 200 1764
[2025-08-05 23:21:58,003][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407317286 HTTP/1.1" 200 1763
[2025-08-05 23:22:00,241][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407319497 HTTP/1.1" 200 1763
[2025-08-05 23:22:03,225][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407322485 HTTP/1.1" 200 1763
[2025-08-05 23:22:06,234][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754407325481 HTTP/1.1" 200 1765
[2025-08-05 23:22:07,770][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407327116 HTTP/1.1" 200 15096
[2025-08-05 23:23:05,327][django.server.log_message():212] [INFO] "GET /api/docs HTTP/1.1" 200 788
[2025-08-05 23:23:09,702][django.server.log_message():212] [INFO] "GET /api/openapi.json HTTP/1.1" 200 154520
[2025-08-05 23:23:52,196][django.request.log_response():241] [WARNING] Unauthorized: /api/mywechat/wechatMiniPros
[2025-08-05 23:23:52,198][django.server.log_message():212] [WARNING] "GET /api/mywechat/wechatMiniPros?pageSize=10&page=1 HTTP/1.1" 401 91
[2025-08-05 23:24:05,939][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407443707 HTTP/1.1" 200 15096
[2025-08-05 23:24:55,812][django.server.log_message():212] [INFO] "POST /api/system/menu HTTP/1.1" 200 542
[2025-08-05 23:24:56,570][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407495815 HTTP/1.1" 200 15586
[2025-08-05 23:25:07,686][django.server.log_message():212] [INFO] "GET /api/system/userinfo?_t=1754407506926 HTTP/1.1" 200 652
[2025-08-05 23:25:08,459][django.server.log_message():212] [INFO] "GET /api/system/permCode?_t=1754407507698 HTTP/1.1" 200 599
[2025-08-05 23:25:09,132][django.server.log_message():212] [INFO] "GET /api/system/menu/route/tree?_t=1754407508462 HTTP/1.1" 200 16126
[2025-08-05 23:25:10,282][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407509552 HTTP/1.1" 200 15586
[2025-08-05 23:25:30,589][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407529916 HTTP/1.1" 200 15586
[2025-08-05 23:25:53,997][django.server.log_message():212] [INFO] "POST /api/system/menu HTTP/1.1" 200 548
[2025-08-05 23:25:54,793][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407554000 HTTP/1.1" 200 16096
[2025-08-05 23:25:57,188][django.server.log_message():212] [INFO] "GET /api/system/userinfo?_t=1754407556299 HTTP/1.1" 200 652
[2025-08-05 23:25:57,900][django.server.log_message():212] [INFO] "GET /api/system/permCode?_t=1754407557199 HTTP/1.1" 200 599
[2025-08-05 23:25:58,666][django.server.log_message():212] [INFO] "GET /api/system/menu/route/tree?_t=1754407557902 HTTP/1.1" 200 16654
[2025-08-05 23:26:01,026][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407560304 HTTP/1.1" 200 16096
[2025-08-05 23:28:03,962][django.server.log_message():212] [INFO] "GET /api/demo/demo?page=1&pageSize=10&_t=1754407683301 HTTP/1.1" 200 90
[2025-08-05 23:28:40,216][django.server.log_message():212] [INFO] "GET /api/mywechat/wechatMiniPros?pageSize=10&page=1 HTTP/1.1" 200 106
[2025-08-05 23:33:19,916][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754407999212 HTTP/1.1" 200 16096
[2025-08-05 23:33:45,253][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754408024562 HTTP/1.1" 200 16096
[2025-08-05 23:33:49,196][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754408028479 HTTP/1.1" 200 16096
[2025-08-05 23:34:33,169][django.server.log_message():212] [INFO] "PUT /api/system/menu/35 HTTP/1.1" 200 566
[2025-08-05 23:34:33,992][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754408073171 HTTP/1.1" 200 16114
[2025-08-05 23:34:37,163][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754408076526 HTTP/1.1" 200 16114
[2025-08-05 23:34:40,285][django.server.log_message():212] [INFO] "GET /api/system/menu_button?page=1&pageSize=10&menu_id=35&_t=1754408079665 HTTP/1.1" 200 90
[2025-08-05 23:34:40,318][django.server.log_message():212] [INFO] "GET /api/system/menu_button?page=1&pageSize=10&menu_id=35&_t=1754408079618 HTTP/1.1" 200 90
[2025-08-05 23:34:45,870][django.server.log_message():212] [INFO] "GET /api/system/menu_column_field?page=1&pageSize=10&menu_id=35&_t=1754408085162 HTTP/1.1" 200 90
[2025-08-05 23:34:50,542][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754408089835 HTTP/1.1" 200 16114
[2025-08-05 23:34:55,777][django.server.log_message():212] [INFO] "GET /api/system/userinfo?_t=1754408094982 HTTP/1.1" 200 652
[2025-08-05 23:34:56,450][django.server.log_message():212] [INFO] "GET /api/system/permCode?_t=1754408095791 HTTP/1.1" 200 599
[2025-08-05 23:34:57,257][django.server.log_message():212] [INFO] "GET /api/system/menu/route/tree?_t=1754408096464 HTTP/1.1" 200 16672
[2025-08-05 23:34:58,436][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754408097688 HTTP/1.1" 200 16114
