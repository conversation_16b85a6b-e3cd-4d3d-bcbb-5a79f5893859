[2025-08-05 21:57:06,066][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\fuadmin\celery.py changed, reloading.
[2025-08-05 21:57:07,400][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 21:58:43,920][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\fuadmin\settings.py changed, reloading.
[2025-08-05 21:58:45,047][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:01:50,887][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\fuadmin\celery.py changed, reloading.
[2025-08-05 22:01:52,127][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:09:44,166][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\Mywechatmini\api.py changed, reloading.
[2025-08-05 22:09:45,403][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:10:03,180][django.utils.autoreload.trigger_reload():266] [INFO] D:\data\code\back\fu-admin-master\backend\Mywechatmini\admin.py changed, reloading.
[2025-08-05 22:10:04,395][django.utils.autoreload.run_with_reloader():668] [INFO] Watching for file changes with StatReloader
[2025-08-05 22:12:39,860][django.server.log_message():212] [INFO] "GET /api/system/userinfo?_t=1754403159089 HTTP/1.1" 200 652
[2025-08-05 22:12:40,506][django.server.log_message():212] [INFO] "GET /api/system/permCode?_t=1754403159874 HTTP/1.1" 200 599
[2025-08-05 22:12:41,219][django.server.log_message():212] [INFO] "GET /api/system/menu/route/tree?_t=1754403160509 HTTP/1.1" 200 15618
[2025-08-05 22:12:44,445][django.server.log_message():212] [INFO] "GET /api/system/menu?_t=1754403163745 HTTP/1.1" 200 15096
[2025-08-05 22:12:47,008][django.server.log_message():212] [INFO] "GET /api/system/post?page=1&pageSize=10&_t=1754403166238 HTTP/1.1" 200 90
[2025-08-05 22:12:48,592][django.server.log_message():212] [INFO] "GET /api/system/dept/list/tree?_t=1754403167920 HTTP/1.1" 200 2209
[2025-08-05 22:12:49,095][django.server.log_message():212] [INFO] "GET /api/system/dept/list/tree?_t=1754403168513 HTTP/1.1" 200 2209
[2025-08-05 22:12:49,104][django.server.log_message():212] [INFO] "GET /api/system/user?page=1&pageSize=10&_t=1754403167991 HTTP/1.1" 200 1264
[2025-08-05 22:12:50,364][django.server.log_message():212] [INFO] "GET /api/system/role?page=1&pageSize=10&_t=1754403169360 HTTP/1.1" 200 834
[2025-08-05 22:12:58,074][django.server.log_message():212] [INFO] "GET /api/demo/demo?page=1&pageSize=10&_t=1754403177481 HTTP/1.1" 200 90
[2025-08-05 22:13:02,546][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403180694 HTTP/1.1" 200 1772
[2025-08-05 22:13:04,510][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403183719 HTTP/1.1" 200 1762
[2025-08-05 22:13:07,437][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403186721 HTTP/1.1" 200 1762
[2025-08-05 22:13:10,431][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403189729 HTTP/1.1" 200 1760
[2025-08-05 22:13:13,448][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403192719 HTTP/1.1" 200 1762
[2025-08-05 22:13:16,410][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403195718 HTTP/1.1" 200 1762
[2025-08-05 22:13:19,418][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403198726 HTTP/1.1" 200 1762
[2025-08-05 22:13:23,955][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403202287 HTTP/1.1" 200 1765
[2025-08-05 22:13:26,082][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403205288 HTTP/1.1" 200 1766
[2025-08-05 22:13:29,065][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403208277 HTTP/1.1" 200 1765
[2025-08-05 22:13:32,058][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403211290 HTTP/1.1" 200 1761
[2025-08-05 22:13:34,989][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403214275 HTTP/1.1" 200 1762
[2025-08-05 22:13:38,020][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403217276 HTTP/1.1" 200 1764
[2025-08-05 22:13:41,061][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403220287 HTTP/1.1" 200 1764
[2025-08-05 22:13:44,062][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403223285 HTTP/1.1" 200 1766
[2025-08-05 22:13:47,060][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403226277 HTTP/1.1" 200 1764
[2025-08-05 22:13:49,948][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403229281 HTTP/1.1" 200 1763
[2025-08-05 22:13:53,045][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403232278 HTTP/1.1" 200 1767
[2025-08-05 22:13:55,972][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403235285 HTTP/1.1" 200 1767
[2025-08-05 22:13:58,935][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403238277 HTTP/1.1" 200 1762
[2025-08-05 22:14:02,072][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403241285 HTTP/1.1" 200 1762
[2025-08-05 22:14:04,977][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403244281 HTTP/1.1" 200 1762
[2025-08-05 22:14:08,056][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403247281 HTTP/1.1" 200 1764
[2025-08-05 22:14:11,034][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403250288 HTTP/1.1" 200 1766
[2025-08-05 22:14:14,007][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403253282 HTTP/1.1" 200 1762
[2025-08-05 22:14:17,061][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403256276 HTTP/1.1" 200 1764
[2025-08-05 22:14:19,942][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403259282 HTTP/1.1" 200 1762
[2025-08-05 22:14:27,995][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403267281 HTTP/1.1" 200 1759
[2025-08-05 22:15:29,060][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403327289 HTTP/1.1" 200 1765
[2025-08-05 22:16:29,072][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403387287 HTTP/1.1" 200 1765
[2025-08-05 22:17:29,009][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403447280 HTTP/1.1" 200 1767
[2025-08-05 22:18:19,525][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403497789 HTTP/1.1" 200 1763
[2025-08-05 22:18:19,525][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403498724 HTTP/1.1" 200 1772
[2025-08-05 22:18:23,005][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403502289 HTTP/1.1" 200 1765
[2025-08-05 22:18:26,026][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403505281 HTTP/1.1" 200 1767
[2025-08-05 22:18:29,045][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403508290 HTTP/1.1" 200 1766
[2025-08-05 22:18:32,023][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403511288 HTTP/1.1" 200 1762
[2025-08-05 22:18:34,948][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403514282 HTTP/1.1" 200 1763
[2025-08-05 22:18:38,076][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403517287 HTTP/1.1" 200 1761
[2025-08-05 22:18:40,957][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403520283 HTTP/1.1" 200 1763
[2025-08-05 22:18:44,001][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403523276 HTTP/1.1" 200 1762
[2025-08-05 22:18:47,041][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403526288 HTTP/1.1" 200 1761
[2025-08-05 22:18:49,960][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403529277 HTTP/1.1" 200 1764
[2025-08-05 22:18:53,013][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403532281 HTTP/1.1" 200 1762
[2025-08-05 22:18:55,964][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403535276 HTTP/1.1" 200 1761
[2025-08-05 22:18:59,018][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403538280 HTTP/1.1" 200 1761
[2025-08-05 22:19:02,118][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403541280 HTTP/1.1" 200 1764
[2025-08-05 22:19:05,038][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403544283 HTTP/1.1" 200 1762
[2025-08-05 22:19:08,077][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403547275 HTTP/1.1" 200 1761
[2025-08-05 22:19:11,035][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403550283 HTTP/1.1" 200 1768
[2025-08-05 22:19:14,089][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403553280 HTTP/1.1" 200 1766
[2025-08-05 22:19:16,994][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403556289 HTTP/1.1" 200 1764
[2025-08-05 22:19:27,965][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403567275 HTTP/1.1" 200 1775
[2025-08-05 22:20:28,997][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403627277 HTTP/1.1" 200 1767
[2025-08-05 22:21:29,006][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403687288 HTTP/1.1" 200 1767
[2025-08-05 22:22:29,061][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403747287 HTTP/1.1" 200 1766
[2025-08-05 22:23:29,047][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403807289 HTTP/1.1" 200 1766
[2025-08-05 22:24:29,031][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403867286 HTTP/1.1" 200 1762
[2025-08-05 22:25:29,084][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403927280 HTTP/1.1" 200 1764
[2025-08-05 22:26:29,035][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754403987282 HTTP/1.1" 200 1765
[2025-08-05 22:27:29,030][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404047282 HTTP/1.1" 200 1764
[2025-08-05 22:28:28,985][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404107281 HTTP/1.1" 200 1778
[2025-08-05 22:29:29,119][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404167280 HTTP/1.1" 200 1765
[2025-08-05 22:30:28,963][django.server.log_message():212] [INFO] "GET /api/system/monitor?_t=1754404227280 HTTP/1.1" 200 1767
