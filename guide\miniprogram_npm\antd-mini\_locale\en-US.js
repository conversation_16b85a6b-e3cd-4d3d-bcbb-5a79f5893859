var enUS = {
    // locales for all components
    locale: 'en-US',
    global: {
        placeholder: 'Please select',
        emptyText: 'No data available',
        okText: 'Ok',
        cancelText: 'Cancel',
    },
    input: {
        placeholder: 'Please enter',
    },
    calendar: {
        weekdayNames: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        today: 'Today',
        start: 'Start',
        end: 'End',
        startAndEnd: 'Start/End',
        format: 'MMM D, YYYY',
    },
    rangePicker: {
        startPlaceholder: 'No selection started',
        endPlaceholder: 'Incomplete selection',
    },
    guideTour: {
        gotItText: 'Got it',
        nextStepText: 'Next step',
        prevStepText: 'Prev step',
        jumpText: 'Skip',
    },
    imageUpload: {
        uploadingText: 'Uploading',
        uploadfailedText: 'Upload failed',
    },
    pageContainer: {
        failed: {
            title: 'The page is experiencing some minor issues',
            message: "I'll try it later",
        },
        disconnected: {
            title: 'The network is a bit busy',
            message: 'Move your fingers to help repair',
        },
        empty: {
            title: "There's nothing here",
            message: 'Check out the others',
        },
        busy: {
            title: 'Congestion ahead',
            message: 'Try refreshing',
        },
    },
};
export default enUS;
