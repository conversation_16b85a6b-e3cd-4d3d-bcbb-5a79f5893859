from django.db import models

# Create your models here.
class Star(models.Model):
    id = models.BigIntegerField(verbose_name="id",auto_created=True, db_index=True, primary_key=True)
    chinese_name = models.Char<PERSON>ield(max_length=50, db_index=True, verbose_name='中文名', help_text="中文名")
    aliases_name = models.CharField(max_length=100, verbose_name='别名' , help_text='别名', null=True,blank=True)
    foreign_name = models.CharField(max_length=50, verbose_name='外文名', help_text='外文名', null=True,blank=True)
    country = models.Char<PERSON>ield(max_length=50, verbose_name='国籍', help_text='国籍', null=True)
    nation = models.CharField(max_length=50, verbose_name='民族', help_text='民族', null=True,blank=True)
    blood_group = models.CharField(max_length=50, verbose_name='血型', help_text='血型', null=True,blank=True)
    constellation = models.Char<PERSON>ield(max_length=50, verbose_name='星座', help_text='星座', null=True,blank=True)

    weight = models.CharField(max_length=50, verbose_name='体重', help_text='体重', null=True,blank=True)
    birthday = models.CharField(max_length=50, verbose_name='生日', help_text='生日', null=True,blank=True)
    birth = models.CharField(max_length=150, verbose_name='出生地', help_text='出生地', null=True,blank=True)
    stature = models.CharField(max_length=150, verbose_name='身高', help_text='身高', null=True,blank=True)
    profession = models.CharField(max_length=150, verbose_name='职业', help_text='职业', null=True,blank=True)

    create_datetime = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_datetime = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = "spider_star"
        verbose_name = '明星'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

