{"name": "@mini-types/alipay", "version": "3.0.14", "description": "TypeScript declarations for Alipa<PERSON>'s mini program.", "scripts": {}, "miniprogram": "./", "repository": {"type": "git", "url": "**************:ant-mini-program/api-typings.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "miniprogram", "types"], "license": "MIT", "types": "./types/index.d.ts", "files": ["types"], "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "dependencies": {"@mini-types/global": "3.0.14", "@mini-types/my": "3.0.14"}, "gitHead": "f923cf356c26bf6c80ca640951683ba777d968f8"}