from django.db import models

from utils.models import CoreModel


# Create your models here.


class MyWechat(CoreModel):
    openid = models.CharField(help_text='小程序OPENID',null=False,blank=False, max_length=32, verbose_name='小程序OPENID')
    name = models.CharField(help_text='小程序名称',null=False,blank=False, max_length=64, verbose_name='小程序名称')
    short_desc = models.Char<PERSON>ield(help_text='小程序简短简介',null=False,blank=False, max_length=64, verbose_name='小程序简短简介')
    desc = models.CharField(help_text='小程序详细简介', null=True, blank=True, max_length=64, verbose_name='小程序详细简介')
    publish_status = models.BooleanField(help_text='发布状态', verbose_name='发布状态',blank=False, default=False)
    icon = models.Char<PERSON><PERSON>(help_text='小程序图标', null=True, blank=True, max_length=64, verbose_name='小程序图标')
    class_id = models.Char<PERSON>ield(help_text='分类ID', null=False, blank=False, max_length=64, verbose_name='分类ID')


class Meta:
    db_table = "MyWechat"
    verbose_name = '小程序'
    ordering = ('-create_datetime',)