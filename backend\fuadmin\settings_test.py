import os
from pathlib import Path
from .settings import *

# 测试环境标识
ENVIRONMENT = 'test'

# 数据库配置 - 使用测试数据库
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "HOST": env.str('DATABASE_HOST', '127.0.0.1'),
        "PORT": env.str('DATABASE_PORT', '3306'),
        "USER": env.str('DATABASE_USER', 'root'),
        "PASSWORD": env.str('DATABASE_PASSWORD', '123456'),
        "NAME": "myweb_dev",  # 使用测试数据库
        "charset": "utf8mb4",
    }
}

# 测试环境日志配置
CELERY_LOG_DIR = os.path.join(BASE_DIR, "logs", "celery")
if not os.path.exists(CELERY_LOG_DIR):
    os.makedirs(CELERY_LOG_DIR)

# Celery日志配置
CELERY_LOG_LEVEL = 'INFO'
CELERY_LOG_FILE = os.path.join(CELERY_LOG_DIR, 'celery.log')
CELERY_ERROR_LOG_FILE = os.path.join(CELERY_LOG_DIR, 'celery_error.log')

# 更新日志配置
LOGGING['handlers']['celery'] = {
    "level": "INFO",
    "class": "logging.handlers.RotatingFileHandler",
    "filename": CELERY_LOG_FILE,
    "maxBytes": 1024 * 1024 * 50,  # 50 MB
    "backupCount": 5,
    "formatter": "standard",
    "encoding": "utf-8",
}

LOGGING['handlers']['celery_error'] = {
    "level": "ERROR",
    "class": "logging.handlers.RotatingFileHandler",
    "filename": CELERY_ERROR_LOG_FILE,
    "maxBytes": 1024 * 1024 * 50,  # 50 MB
    "backupCount": 3,
    "formatter": "standard",
    "encoding": "utf-8",
}

# 添加celery日志记录器
LOGGING['loggers']['celery'] = {
    "handlers": ["console", "celery", "celery_error"],
    "level": "INFO",
    "propagate": False,
}

LOGGING['loggers']['celery.task'] = {
    "handlers": ["console", "celery", "celery_error"],
    "level": "INFO",
    "propagate": False,
}

LOGGING['loggers']['celery.worker'] = {
    "handlers": ["console", "celery", "celery_error"],
    "level": "INFO",
    "propagate": False,
}

# 测试环境禁用某些功能
DEBUG = True
CELERY_TASK_ALWAYS_EAGER = False  # 测试环境仍然使用异步任务