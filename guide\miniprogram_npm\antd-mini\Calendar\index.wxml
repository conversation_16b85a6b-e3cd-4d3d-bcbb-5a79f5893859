<wxs
  src="./helper.wxs"
  module="helper"
></wxs>
<wxs
  src="./scroll.wxs"
  module="scroll"
></wxs>
<view
  class="ant-calendar {{className ? className : ''}}"
  style="{{style}}"
>
  <view class="ant-calendar-mark">
    <block
      wx:for="{{markItems}}"
      wx:for-index="index"
      wx:for-item="item"
      wx:key="*this"
    >
      <view class="{{helper.getMarkCellClassName(index, markItems)}}">
        <!--display: inline-->
        <ant-auto-resize
          wx:if="{{item}}"
          text="{{item}}"
          minFontSize="13"
          maxFontSize="28"
        >
          <text>{{item}}</text>
        </ant-auto-resize>
        <ant-auto-resize
          wx:else
          text="{{item}}"
          minFontSize="13"
          maxFontSize="28"
        >
          <block>
            <text name="item">{{item}}</text>
          </block>
        </ant-auto-resize>
        <text>{{item}}</text>
      </view>
    </block>
  </view>
  <view
    wx:if="{{!!elementSize}}"
    class="ant-calendar-sticky"
  >
    <view class="ant-calendar-sticky-title">
      <view class="ant-calendar-title">{{monthList[headerState].title}}</view>
    </view>
  </view>
  <scroll-view
    scrollY="{{true}}"
    class="ant-calendar-body"
    data-elementsize="{{elementSize}}"
    data-monthlist="{{monthList}}"
    bindscroll="{{scroll.handleScroll}}"
    scrollIntoView="{{scrollIntoViewId}}"
    scrollWithAnimation
    scrollAnimationDuration="{{300}}"
    disableLowerScroll="out-of-bounds"
    disableUpperScroll="out-of-bounds"
    bindref="handleRef"
  >
    <block
      wx:for="{{monthList}}"
      wx:for-index="index"
      wx:for-item="currentMonth"
      wx:key="title"
    >
      <view
        class="ant-calendar-body-container {{currentMonth.className || ''}}"
        style="{{currentMonth.style || ''}}"
      >
        <view class="ant-calendar-title-container">
          <view class="ant-calendar-title">{{currentMonth.title}}</view>
        </view>
        <view class="ant-calendar-cells">
          <block
            wx:for="{{currentMonth.cells}}"
            wx:for-index="index"
            wx:for-item="item"
            wx:key="time"
          >
            <block wx:if="{{helper.isDisplay(index, currentMonth.cells)}}">
              <view
                class="{{helper.getClassName(item, index, showSelectableDatesOnly)}}"
                id="id_{{item.time}}"
                data-time="{{item}}"
                bindtap="clickCell"
              >
                <view class="ant-calendar-cell-container">
                  <view class="ant-calendar-cell-top">
                    <!--display: inline-->
                    <ant-auto-resize
                      wx:if="{{item.top.label}}"
                      text="{{item.top.label}}"
                      minFontSize="13"
                      maxFontSize="18"
                    >
                      <text
                        wx:if="{{item.top}}"
                        class="ant-calendar-cell-top-text {{item.top.className ? item.top.className : ''}}"
                      >{{item.top.label}}</text>
                    </ant-auto-resize>
                    <ant-auto-resize
                      wx:else
                      text="{{item.top.label}}"
                      minFontSize="13"
                      maxFontSize="18"
                    >
                      <block>
                        <text
                          wx:if="{{item.top}}"
                          class="ant-calendar-cell-top-text {{item.top.className ? item.top.className : ''}}"
                        >{{item.top.label}}</text>
                      </block>
                    </ant-auto-resize>
                  </view>
                  <view class="ant-calendar-cell-center">{{item.date}}</view>
                  <view class="ant-calendar-cell-bottom">
                    <!--display: inline-->
                    <text
                      wx:if="{{item.bottom}}"
                      class="{{item.bottom.className}}"
                    >{{item.bottom.label}}</text>
                  </view>
                </view>
              </view>
              <view
                wx:if="{{index % 7 !== 6}}"
                class="{{helper.getSpaceClassName(index, currentMonth.cells)}}"
              ></view>
            </block>
          </block>
        </view>
      </view>
    </block>
  </scroll-view>
</view>