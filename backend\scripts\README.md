# 微博爬虫优化说明

## 优化内容

### 1. 环境配置优化
- 新增测试环境配置文件 `fuadmin/settings_test.py`
- 支持使用 `myweb_dev` 数据库进行功能测试
- 优化Celery日志配置，便于Docker环境查看日志

### 2. 微博爬虫代码优化
- 修复字段名错误：`hot_tile` -> `hot_title`
- 增加完整的错误处理和日志记录
- **ODS数据层设计**：不做去重处理，每个时间点都保存完整热搜记录
- 增加请求头，提高爬取成功率
- 优化任务调度，每10分钟执行一次，用于数据存档分析

### 3. Celery配置优化
- 增加专门的Celery日志配置
- 添加任务重试和错误处理机制
- 优化任务结果存储配置

## 使用方法

### 环境切换

#### 方法1：使用批处理脚本（推荐）
```bash
# 启动测试环境
backend/scripts/start_test_env.bat

# 启动生产环境
backend/scripts/start_prod_env.bat
```

#### 方法2：使用Python脚本
```bash
# 切换到测试环境
python backend/scripts/switch_env.py test

# 切换到生产环境
python backend/scripts/switch_env.py production
```

#### 方法3：手动设置环境变量
```bash
# Windows
set DJANGO_ENV=test
set DJANGO_SETTINGS_MODULE=fuadmin.settings_test

# Linux/Mac
export DJANGO_ENV=test
export DJANGO_SETTINGS_MODULE=fuadmin.settings_test
```

### 启动Celery服务

#### 测试环境
```bash
# 启动Worker
python manage.py celery worker --loglevel=info

# 启动Beat（定时任务）
python manage.py celery beat --loglevel=info
```

#### 生产环境（Docker）
```bash
docker-compose up celery
```

### 测试微博爬虫

#### 运行测试脚本
```bash
python backend/scripts/test_weibo_spider.py
```

#### 数据分析
```bash
# 分析热搜趋势
python backend/scripts/analyze_weibo_data.py

# 分析特定热搜词
python backend/scripts/analyze_weibo_data.py --word "关键词"
```

#### 手动测试
```python
# 在Django shell中测试
python manage.py shell

>>> from spiders.weibo import get_weibo_hot
>>> result = get_weibo_hot()
>>> print(result)
```

## 日志文件位置

### 测试环境
- Celery日志：`backend/logs/celery/celery.log`
- Celery错误日志：`backend/logs/celery/celery_error.log`

### 生产环境
- 应用日志：`backend/logs/server.log`
- 错误日志：`backend/logs/error.log`

## 数据库配置

### 测试环境
- 数据库：`myweb_dev`
- 用途：功能测试，不影响生产数据

### 生产环境
- 数据库：`myweb`
- 用途：正式数据存储

## 定时任务配置

微博热搜爬虫已配置为每10分钟执行一次，用于ODS数据层存储：
```python
'get_weibo_hot_task': {
    'task': 'spiders.weibo.get_weibo_hot',
    'schedule': crontab(minute='*/10'),  # 每10分钟执行
}
```

### ODS数据层设计理念

- **数据完整性**：每个时间点都保存完整的热搜记录，不做去重处理
- **时间序列分析**：可以分析某个热搜的持续时间、排名变化等
- **数据存档**：作为原始数据存储，后续可通过ETL处理到DM层
- **分析价值**：可以分析热搜趋势、热点变化、用户关注度等

### 数据分析功能

提供了数据分析脚本 `analyze_weibo_data.py`，支持：
- 热搜词出现频率统计
- 热搜持续时间分析
- 排名变化趋势分析
- 标签和分类统计
- 特定热搜词详细分析

## 注意事项

1. **环境切换**：确保在启动Celery服务前正确设置环境变量
2. **数据库连接**：测试环境使用 `myweb_dev` 数据库，不会影响生产数据
3. **日志查看**：Docker环境中可以通过挂载日志目录来查看日志
4. **ODS数据存储**：不做去重处理，每个时间点都保存完整记录用于后续分析
5. **错误处理**：增加了完整的异常处理，失败时会记录详细错误信息

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 确认数据库名称是否正确（测试环境：myweb_dev）

2. **Celery任务不执行**
   - 检查Redis服务是否正常
   - 确认Worker和Beat是否都正常启动

3. **日志文件不存在**
   - 检查logs目录权限
   - 确认目录是否自动创建

4. **爬虫获取数据失败**
   - 检查网络连接
   - 查看错误日志获取详细信息
   - 可能需要更新请求头或代理设置 