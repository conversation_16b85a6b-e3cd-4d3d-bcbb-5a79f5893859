export var cssVariables = {
    // primary
    'color-brand1': '#3086ff',
    'color-border-faded': 'rgba(48, 134, 255, 0.9)',
    'color-brand2': '#0a0a0a',
    // secondary
    'color-link': '#3f5980',
    'color-wathet': '#0d2543',
    'color-orange': '#e65a2b',
    'color-yellow': '#ffa930',
    'color-green': '#34b368',
    'color-red': '#ff4a58',
    // neutral
    'color-text-primary': '#c5cad1',
    'color-text-secondary': '#808080',
    'color-text-assist': '#616161',
    'color-text-weak': '#474747',
    'color-text-weak-faded': 'rgba(71, 71, 71, 0.6)',
    'color-border': '#2b2b2b',
    'color-border-greycard': '#444444',
    'color-background2': '#121212',
    'color-background': '#121212',
    'color-card': '#1a1a1a',
    'color-card2': '#222222',
    'color-white-card': '#2b2b2b',
    'color-grey-card': '#2b2b2b',
    'color-black-card': '#525252',
    'color_text_secondary': '#808080',
    // reverse
    'color-white': '#ffffff',
    'color-black': '#000000',
    'color-black-fade': 'rgba(0, 0, 0, 1)',
    'color-white-dynamic': '#c5cad1',
    'color-white-change': '#000000',
    'color-black-change': '#ffffff',
    // 以下没在颜色系统中
    'color-golden-2': '#fff3d9',
    'color-golden-3': '#fff9ed',
    'color-tangerine-1': '#ff6010',
    'color-tangerine-2': '#ffece3',
    'color-grey-2': '#444444',
    'color-text-warning': '#ff3b30',
    'color-orange-1': '#ff8f1f',
    'color-orange-2': '#ffefdf',
    'color-pomonagreen-1': '#00b578',
    'color-pomonagree-2': '#d4fff1',
    // radius
    'size-radius-xs': '8rpx',
    'size-radius-s': '16rpx',
    'size-radius-m': '24rpx',
    'size-radius-l': '32rpx',
    'size-radius-xl': '40rpx',
    'size-radius-xxl': '48rpx',
    // space
    'size-space-page-margin': '16rpx',
    'size-space1': '4rpx',
    // opacity
    'opacity-press': '0.08',
    'opacity-disabled': '0.4',
    'opacity-mask': '0.55',
    'opacity-module': '0.9',
    // font-size
    'size-font1': '22rpx',
    'size-font2': '24rpx',
    'size-font3': '26rpx',
    'size-font4': '28rpx',
    'size-font5': '30rpx',
    'size-font6': '32rpx',
    'size-font7': '34rpx',
    'size-font8': '36rpx',
    'size-font9': '40rpx',
    'size-font10': '48rpx',
    'size-font11': '56rpx',
    'size-font12': '64rpx',
    // popoverList
    'popover-content-bg': 'rgba(255, 255, 255, 0.45)',
    'popover-content-color': '#c5cad1',
    // button
    'button-disabled-opacity': '0.4',
    'button-color': '#ff4040',
    'button-background-color': '#000000',
    'button-border-color': '#0a0a0a',
    'button-primary-border-color': '#3086ff',
    'button-primary-background-color': '#000000',
    'button-primary-color': '#ffffff',
    'button-primary-aide-border-color': '#0d2543',
    'button-primary-aide-color': '#3086ff',
    'button-default-aide-border-color': '#121212',
    'button-default-aide-background-color': '#000000',
    'button-default-aide-color': '#c5cad1',
    'button-primary-danger-background-color': '#ff4a58',
    'button-primary-danger-border-color': '#ff4a58',
    'button-primary-danger-color': '#ffffff',
    'button-default-danger-background-color': '#ffffff',
    'button-default-danger-border-color': '#ff4a58',
    'button-default-danger-color': '#ff4a58',
    'button-text-danger-color': '#ff4a58',
    'button-danger-default-color': '#ff4a58',
    'button-active-bg': 'rgba(255, 255, 255, 0.08)',
    // actionsheet
    'actionsheet-danger-color': '#ff4a58',
    'actionsheet-title-color': '#616161',
    'actionsheet-item-color': '#c5cad1',
    'actionsheet-item-active-bg': '#2b2b2b',
    'activesheet-item-cancel-bg': '#121212',
    // icon
    'icon-color': '#c5cad1',
    // tabbar
    'tabbar-bg': '#1a1a1a',
    'tabbar-item-color': '#808080',
    'tabbar-active-color': '#3086ff',
    // tabs
    'tabs-basic-color': '#c5cad1',
    'tabs-weaken-color': '#616161',
    'tabs-inverse-color': '#1a1a1a',
    'tabs-active-color': '#3086ff',
    'tabs-active-decorate-color': '#3086ff',
    'tabs-underline-border-color': '#2b2b2b',
    'tabs-plus-color': '#000000',
    'tabs-capsule-title-bg': '#121212',
    'tabs-subtitle-color': '#ffffff',
    'tabs-count-color': '#474747',
    'tabs-badge-size': '28rpx',
    // feedBack
    'feedback-text-color': '#c5cad1',
    'feedback-background-color': 'rgba(216, 216, 216, 0.42)',
    'feedback-mask-color': 'rgba(0, 0, 0, 0.25)',
    'feedback-content-background-color': '#ffffff',
    'feedback-list-background-color': '#121212',
    'feedback-list-text-color': '#c5cad1',
    // calendar
    'calendar-cell-disabled-opacity': '0.4',
    'calendar-weekday-names-bg': '#f8f8f8',
    'calendar-default-color': '#c5cad1',
    'calendar-selected-color': 'rgba(22, 119, 255, 0.1)',
    'calendar-assist-color': '#616161',
    'calendar-selected-end-color': '#ffffff',
    // collapse
    'collapse-title-background-color': '#1a1a1a',
    'collapse-title-color': '#c5cad1',
    'collapse-title-icon-color': '#474747',
    'collapse-content-background-color': '#1a1a1a',
    'collapse-border-color': '#2b2b2b',
    'collapse-node-text-color': '#616161',
    'collapse-container-background-color': '#000000',
    // container
    'container-header-color': '#c5cad1',
    'container-background-color': '#1a1a1a',
    // divider
    'divider-text-color': '#c5cad1',
    'divider-border-color': '#444444',
    // empty
    'empty-text-color': '#c5cad1',
    'empty-asisst-text-color': '#616161',
    // grid
    'ant-grid-title-color': '#c5cad1',
    'ant-grid-description-color': '#616161',
    'ant-grid-border-color': '#2b2b2b',
    // guide-tour
    'guide-tour-text-color': '#ffffff',
    'guide-tour-clear-color': '#616161',
    'guide-tour-dot-color': '#616161',
    'guide-tour-border-color': '#2b2b2b',
    'guide-tour-btn-color': '#c5cad1',
    // indexbar
    'index-bar-tip-background-color': '#474747',
    'index-bar-text-color': '#ffffff',
    'index-bar-assist-color': '#616161',
    'index-bar-active-color': '#3086ff',
    // input
    'input-item-color': '#c5cad1',
    'input-item-placeholder-color': '#474747',
    'input-item-clear-color': '#616161',
    'input-background-color': '#1a1a1a',
    // list
    'list-header-color': '#616161',
    'list-footer-color': '#616161',
    'list-background-color': '#1a1a1a',
    'list-content-color': '#c5cad1',
    'list-extra-brief-color': '#616161',
    'list-item-border-color': '#2b2b2b',
    'list-item-text-color': '#474747',
    // progress
    'progress-stroke-color': '#3086ff',
    'progress-trail-color': '#121212',
    'progress-success-color': '#34b368',
    'progress-indicator-color': '#c5cad1',
    'progress-exception-color': '#ff4a58',
    'progress-assist-color': '#616161',
    // steps
    'steps-non-active-bg': '#444444',
    'steps-title-color': '#c5cad1',
    'steps-desc-color': '#616161',
    'steps-default-bg': '#3086ff',
    'steps-finish-bg': '#ff4a58',
    // swipe-action
    'swipe-action-color': '#ffffff',
    // tag
    'tag-primary-color': '#3086ff',
    'tag-warning-color': '#e65a2b',
    'tag-danger-color': '#ff4a58',
    'tag-success-color': '#34b368',
    'tag-primary-light-color': '#0d2543',
    'tag-warning-light-color': '#ffefdf',
    'tag-danger-light-color': '#ffece3',
    'tag-success-light-color': '#d4fff1',
    'tag-base-color': '#ffffff',
    'tag-primary-color-faded': 'rgba(22, 119, 255, 0.3)',
    'tag-warning-color-faded': 'rgba(255, 100, 48, 0.7)',
    'tag-danger-color-faded': 'rgba(255, 49, 65, 0.7)',
    'tag-success-color-faded': 'rgba(34, 179, 94, 0.7)',
    // checkbox
    'checkbox-header-color': '#616161',
    'checkbox-border-color': '#474747',
    'checkbox-background-color': '#3086ff',
    'checkbox-disabled-background': '#121212',
    'checkbox-text-color': '#c5cad1',
    'checkbox-fake-icon-background-color': '#1a1a1a',
    // checkboxList
    'checklist-background-color': '#3086ff',
    'check-list-item-background-color': '#1a1a1a',
    'check-list-item-hover-background-color': '#2b2b2b',
    'check-list-item-content-color': '#c5cad1',
    'check-list-item-description-color': '#616161',
    // data-picker
    'range-picker-shadow-color': '#000000',
    'range-picker-item-color': '#c5cad1',
    'range-picker-active-color': '#3086ff',
    'range-picker-placeholder-color': '#474747',
    'range-picker-shadow-color-faded': 'rgba(0, 0, 0, 0.9)',
    // form
    'form-text-color': '#474747',
    'form-item-color': '#808080',
    'form-item-bg': '#1a1a1a',
    'form-error-color': '#ff4a58',
    'form-extra-color': '#616161',
    'form-asterisk-color': '#ff3b30',
    // imageUpload
    'image-upload-wrapper-background': '#121212',
    'image-upload-cover-background': 'rgba(0, 0, 0, 0.4)',
    'image-upload-text-color': '#ffffff',
    'image-upload-background-color': '#1a1a1a',
    // loading
    'loading-text-color': '#454955',
    'loading-icon-light-color': '#999',
    // mask
    'mask-background-color': 'rgba(0, 0, 0, 0.55)',
    // numberKeyboard
    'number-key-board-active-background-color': '#d3d3d3',
    'number-key-board-text-color': '#c5cad1',
    'number-key-board-none-text-color': '#616161',
    'number-key-board-iphonex-safe-background-color': '#1a1a1a',
    'number-key-board-background-color': '#121212',
    'number-key-board-border-color': '#2b2b2b',
    'number-key-board-transfer-color': '#3086ff',
    // picker
    'picker-item-color': '#c5cad1',
    'picker-header-action-color': '#3086ff',
    'picker-placeholder-color': '#474747',
    'picker-header-color': '#2b2b2b',
    'picker-content-background-color': '#1a1a1a',
    'picker-mask-bg-faded-95': 'rgba(255, 255, 255, 0.05)',
    'picker-mask-bg-faded-60': 'rgba(255, 255, 255, 0.4)',
    // radio
    'radio-header-color': '#616161',
    'radio-border-color': '#474747',
    'radio-background-color': '#3086ff',
    'radio-disabled-background': '#121212',
    'radio-text-color': '#c5cad1',
    'radio-fake-icon-background-color': '#1a1a1a',
    'radio-icon-color': '#ffffff',
    // popoverList
    'popover-list-content-bg': 'rgba(0, 0, 0, 0.93)',
    'popover-list-content-color': '#c5cad1',
    'popover-list-badge-color': '#ff411c',
    // rare-words
    'rare-words-bg': 'rgba(0, 0, 0, 0.45)',
    'rare-keyboard-bg': '#4a4a4a',
    'rare-words-keyboard-color': '#c5cad1',
    'rare-words-border-color': '#2b2b2b',
    'rare-words-kb-bg': '#121212',
    'rare-words-pinyin-key-color': '#ffffff',
    'rare-words-inner-bg': '#474747',
    'rare-words-active-color': '#3086ff',
    'rare-words-pinyin-color': '#121212',
    'rare-words-keyboard-bg': '#4a4a4a',
    'rare-words-item-tips-color': '#808080',
    // selector
    'selector-background-color': '#1a1a1a',
    'selector-item-background': '#121212',
    'selector-item-active-background': '#0d2543',
    'selector-item-color': '#c5cad1',
    'selector-item-sub-color': '#616161',
    // slide
    'slide-block-shadow': 'rgba(0, 0, 0, 0.12)',
    'slider-default-primary-color': '#3086ff',
    'slider-track-bg': '#1a1a1a',
    'slider-fill-bg': '#121212',
    'slider-number-color': '#c5cad1',
    'slide-block-bg': '#ffffff',
    'slide-back-bg': '#121212',
    // stepper
    'stepper-handler-tap-bg': '#ddd',
    'stepper-handler-border-color': '#444444',
    'stepper-border-color': '#444444',
    'stepper-background-color': '#ffffff',
    'stepper-hover-bg': 'rgba(0, 0, 0, 0.92)',
    // switch
    'switch-fill': '#3086ff',
    'switch-border-color': '#2b2b2b',
    'switch-loading-color': '#3086ff',
    'switch-handle-bg': '#ffffff',
    'switch-inner-color': '#616161',
    // modal
    'modal-background-color': '#1a1a1a',
    'modal-title-color': '#c5cad1',
    'modal-content-color': '#c5cad1',
    'modal-close-text-color': '#616161',
    // popover
    'color-background-popover': '#404040',
    'popover-bg': '#1a1a1a',
    'popover-text-color': '#000000',
    'popover-inner-color': '#ff4a58',
    // popup
    'popup-background': '#1a1a1a',
    'popup-color': '#c5cad1',
    'popup-assit-color': '#616161',
    'popup-mask-close-bg': 'rgba(0, 0, 0, 1)',
    // rate
    'rate-active-icon': '#ffa930',
    'rate-icon-color': '#2b2b2b',
    // result
    'result-main-background': '#1a1a1a',
    'result-title-color': '#000000',
    'result-desc-color': '#c5cad1',
    'result-success-color': '#3086ff',
    'result-error-color': '#ff3b30',
    'result-warning-color': '#ff8f1f',
    'result-wait-color': '#00b578',
    // skeleton
    'skeleton-animation-25': '#2b2b2b',
    'skeleton-animation-37': 'rgba(204, 204, 204, 0.6)',
    'skeleton-animation-63': '#2b2b2b',
    // toast
    'toast-default-bg': 'rgba(0, 0, 0, 0.75)',
    'toast-default-color': '#ffffff',
    // badge
    'badge-text-color': '#ffffff',
    'badge-background-color': '#ff4a58',
    // notice-bar
    'notice-background-color': '#fff9ed',
    'notice-border-color': '#fff3d9',
    'notice-color': '#ff6010',
    'notice-error-border-color': '#fff3d9',
    'notice-error-color': '#ffffff',
    'notice-error-background-color': '#ff4a58',
    'notice-primary-border-color': 'rgba(22, 119, 255, 0.72)',
    'notice-primary-color': '#3086ff',
    'notice-primary-background-color': 'rgba(208, 228, 255, 1)',
    'notice-info-text-color': '#ffffff',
    'notice-info-background-color': '#808080',
    // page-container
    'page-container-background-color': '#121212',
    // typography
    'typography-container-color': '#c5cad1',
    // table
    'table-item-color': '#c5cad1',
    'table-item-bg': '#1a1a1a',
    'table-shadow-color': 'rgba(5, 5, 5, 0.06)',
    'table-empty-bg': '#c5cad1',
    // sticky
    'sticky-check-bg': 'rgba(238, 238, 238, 0)',
};
