#!/usr/bin/env python
"""
Docker环境日志查看脚本
用于查看Celery和应用的日志文件
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime

def view_logs(log_type='all', lines=50, follow=False):
    """
    查看日志文件
    
    Args:
        log_type (str): 日志类型 ('celery', 'app', 'all')
        lines (int): 显示的行数
        follow (bool): 是否实时跟踪
    """
    project_root = Path(__file__).parent.parent
    logs_dir = project_root / 'logs'
    
    if not logs_dir.exists():
        print(f"日志目录不存在: {logs_dir}")
        return
    
    log_files = []
    
    if log_type in ['celery', 'all']:
        celery_dir = logs_dir / 'celery'
        if celery_dir.exists():
            log_files.extend([
                celery_dir / 'celery.log',
                celery_dir / 'celery_error.log'
            ])
    
    if log_type in ['app', 'all']:
        log_files.extend([
            logs_dir / 'server.log',
            logs_dir / 'error.log'
        ])
    
    if not log_files:
        print("没有找到日志文件")
        return
    
    print(f"查看日志文件 (显示最后 {lines} 行):")
    print("=" * 60)
    
    for log_file in log_files:
        if log_file.exists():
            print(f"\n📄 {log_file.name}:")
            print("-" * 40)
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    # 读取最后N行
                    all_lines = f.readlines()
                    last_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                    
                    for line in last_lines:
                        print(line.rstrip())
                        
                    if follow:
                        print(f"\n🔄 实时跟踪 {log_file.name} (Ctrl+C 退出)...")
                        # 这里可以实现实时跟踪功能
                        # 由于复杂性，暂时跳过
                        
            except Exception as e:
                print(f"读取日志文件失败: {e}")
        else:
            print(f"❌ 日志文件不存在: {log_file}")

def main():
    parser = argparse.ArgumentParser(description='查看Docker环境日志')
    parser.add_argument('--type', choices=['celery', 'app', 'all'], default='all',
                       help='日志类型 (celery/app/all)')
    parser.add_argument('--lines', type=int, default=50,
                       help='显示的行数 (默认: 50)')
    parser.add_argument('--follow', '-f', action='store_true',
                       help='实时跟踪日志')
    
    args = parser.parse_args()
    
    view_logs(args.type, args.lines, args.follow)

if __name__ == '__main__':
    main() 