from django.contrib import admin
from .models import WechatUser


@admin.register(WechatUser)
class WechatUserAdmin(admin.ModelAdmin):
    """微信用户管理后台"""
    list_display = ('user', 'truncated_openid', 'last_login')
    search_fields = ('username', 'openid')
    list_filter = ('last_login',)

    def truncated_openid(self, obj):
        return f"{obj.openid[:6]}..."  # 隐私保护

    truncated_openid.short_description = 'OpenID'

