.ant-badge {
  display: inline-block;
  position: relative;
}
.ant-badge-content {
  position: absolute;
  display: flex;
  height: 28rpx;
  align-self: center;
  align-items: center;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  box-sizing: border-box;
  word-break: keep-all;
  justify-content: center;
  top: 0;
  left: 100%;
  transform: translate(-50%, -50%);
}
.ant-badge-content-stroke {
  border: 2rpx solid var(--badge-text-color, #ffffff);
}
.ant-badge-content-text {
  padding-left: 4rpx;
}
.ant-badge-content-text:empty {
  display: none;
}
.ant-badge-content .ant-icon {
  font-size: 18rpx;
  color: var(--badge-text-color, #ffffff);
}
.ant-badge-content-not-dot {
  min-width: 28rpx;
  height: 28rpx;
  border-radius: 28rpx;
  display: flex;
  background-color: var(--badge-background-color, #ff3141);
}
.ant-badge-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: var(--badge-background-color, #ff3141);
}
.ant-badge-dot-stroke {
  border: 2rpx solid var(--badge-text-color, #ffffff);
}
.ant-badge-number,
.ant-badge-text,
.ant-badge-bubble {
  color: var(--badge-text-color, #ffffff);
}
