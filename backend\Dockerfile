# 使用 python:3.12-slim-buster 作为基础镜像
FROM python:3.12.2-slim AS build

# 设置工作目录
WORKDIR /app
COPY . /app

# 替换 apt-get 镜像源为阿里云的源
RUN echo "deb http://mirrors.aliyun.com/debian/ buster main non-free contrib" > /etc/apt/sources.list \
    && echo "deb-src http://mirrors.aliyun.com/debian/ buster main non-free contrib" >> /etc/apt/sources.list \
    && apt-get update --fix-missing \
    && apt-get install -y --no-install-recommends \
    supervisor \
    bash \
    wget \
    bzip2 \
    ca-certificates \
    gcc \
    curl \
    git \
    pkg-config \
    default-libmysqlclient-dev \
    libmariadb-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建虚拟环境并激活它

RUN python -m venv /app/venv \
    && chmod 777  /app/venv/bin/activate \
    && /app/venv/bin/activate \
    && /app/venv/bin/pip install --upgrade pip \
    && /app/venv/bin/pip install -r /app/requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ --default-timeout=1000
# 使用一个更小的镜像来作为最终镜像
FROM python:3.12.2-slim AS runtime

# 设置工作目录
WORKDIR /app

# 复制虚拟环境及应用代码
COPY --from=build /app /app

# 设置环境变量，使得使用虚拟环境中的 Python 和 pip
ENV PATH="/app/venv/bin:$PATH"

# 复制并设置 Supervisor 配置文件
COPY ./supervisord.conf /etc/supervisord.conf

RUN chmod 777 /app/venv/bin/activate && /app/venv/bin/activate

# 替换 apt-get 镜像源为阿里云的源
RUN echo "deb http://mirrors.aliyun.com/debian/ buster main non-free contrib" > /etc/apt/sources.list \
    && echo "deb-src http://mirrors.aliyun.com/debian/ buster main non-free contrib" >> /etc/apt/sources.list \
    && apt-get update --fix-missing \
    && apt-get install -y --no-install-recommends supervisor \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 暴露端口
EXPOSE 8000

# 启动 Supervisor 以运行应用
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]

