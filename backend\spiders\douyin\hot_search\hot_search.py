#!/usr/bin/env python
# -*- coding: utf-8 -*-
# time: 2025/8/4
# file: hot_search.py
# author: AI Assistant
# 抖音热搜爬虫实现

import logging
import time
from pathlib import Path

from DrissionPage import ChromiumOptions, Chromium
from DrissionPage.errors import ElementLostError, ContextLostError

from spiders.models import TiktokHotSearch
import random
from utils.StringUtils import StringUtils

logger = logging.getLogger(__name__)

class TiktokHotSearchSpider:
    """抖音热搜爬虫类"""
    def __init__(self):
        self.max_retries = 3
        self.page = None
        try:
            # 创建浏览器配置
            logger.info('尝试创建浏览器实例...')
            from DrissionPage import ChromiumOptions, ChromiumPage
            co = ChromiumOptions()
            co.headless(True)
            co.mute(True)
            co.set_argument('--window-size', '1920,1080')
            co.set_argument('--blink-settings=imagesEnabled=false')
            # 不设置固定端口
            co.set_argument('--remote-debugging-port=0')
            logger.info('浏览器配置完成')
            
            # 创建页面对象
            self.page = ChromiumPage(co)
            # 配置页面参数
            self.page.set.retry_times(3)
            self.page.set.timeouts(20)
            logger.info('浏览器实例创建成功')
        except Exception as e:
            logger.error(f'浏览器初始化失败: {str(e)}')
            raise

    def _configure_browser(self):
        """配置浏览器参数"""
        self.co.headless(True)  # 使用无头模式提高速度
        self.co.mute(True)  # 静音
        self.co.set_argument('--window-size', '1920,1080')
        # 禁用图像加载以提高速度
        self.co.set_argument('--blink-settings=imagesEnabled=false')
        # 添加更多优化参数
        self.co.set_argument('--disable-gpu')
        self.co.set_argument('--no-sandbox')
        self.co.set_argument('--disable-dev-shm-usage')
        self.co.set_argument('--disable-extensions')
        # 不设置固定端口，让系统自动分配
        logger.info('浏览器配置完成，不使用固定端口')

    def _config_page(self):
        """配置page参数"""
        self.chrome.set.retry_times(3)
        self.chrome.set.timeouts(20)
        # 获取浏览器实际使用的端口
        try:
            # 获取调试地址
            debug_addr = self.chrome.debugging_address
            if debug_addr:
                port = debug_addr.split(':')[-1]
                logger.info(f'浏览器配置完成，实际使用端口: {port}')
            else:
                logger.info('浏览器配置完成，无法获取调试地址')
        except Exception as e:
            logger.warning(f'获取浏览器端口信息失败: {str(e)}')
            logger.info('浏览器配置完成')

    def _get_hot_search_data(self):
        """提取热搜数据"""
        try:
            # 关闭登录弹窗
            try:
                # 尝试查找并关闭登录弹窗
                try:
                    # 直接查找关闭按钮并点击
                    close_btn = self.page.ele('@class=wSyUzWHW', timeout=3)
                    if close_btn:
                        close_btn.click()
                        logger.info("已关闭登录弹窗")
                except Exception as e:
                    logger.warning(f"关闭登录弹窗失败: {str(e)}")
            except Exception as e:
                logger.info("没有找到登录弹窗或关闭失败")

            # 等待热搜列表加载 - 优化超时时间并使用更高效的选择器
            try:
                # 使用复合选择器同时等待多种可能的元素
                self.page.wait.eles_loaded('@|class=XdWzFBnx@|data-e2e=feed-right-list-container', timeout=8)
            except Exception as e:
                logger.warning(f"等待热搜列表加载超时: {str(e)}")
                # 不抛出异常，继续尝试获取元素
                
            # 获取热搜列表项 - 优化选择器策略
            # 使用复合选择器一次性尝试多种可能
            hot_list = self.page.ele('@|class=XdWzFBnx@|data-e2e=feed-right-list-container@|tag:div@@class=feed-right-container', timeout=3)
            
            # 获取热搜项
            hot_items = []
            if hot_list:
                # 优先使用最可能的class，设置较短超时
                hot_items = hot_list.eles('@|class=NINGm7vw@|class=hot-search-item', timeout=3)
            
            # 如果仍未找到，再尝试其他选择器
            if not hot_items:
                hot_items = self.page.eles('@|class=hot-search-item@|class=QnYOrJkj@|//div[contains(@class, "hot-search-item")]', timeout=3)
            
            logger.info(f'找到 {len(hot_items)} 条热搜数据')

            hot_search_data = []
            for idx, item in enumerate(hot_items):
                try:
                    # 提取热搜排名 - 使用hot_div.html中的class
                    hot_rank = ''
                    # 调试用print语句已移除
                    logger.debug(f'热搜项原始文本: {item.text}')
                    rank_ele = item.ele('@class=CjXX0j55')
                    if rank_ele:
                        # 尝试获取文本排名
                        hot_rank = rank_ele.text.strip()
                        # 如果没有文本（可能是置顶热搜），检查是否有图片
                        if not hot_rank:
                            img_ele = rank_ele.ele('tag:img')
                            if img_ele:
                                # 置顶热搜默认为排名1
                                hot_rank = '1'
                    
                    # 如果上面的方法没找到，尝试其他class
                    if not hot_rank:
                        # 使用更灵活的查找方式，避免使用固定索引
                        rank_ele_alt = item.ele('@class=rank')
                        if rank_ele_alt:
                            hot_rank = rank_ele_alt.text.strip()
                    
                    if not hot_rank:
                        rank_ele_alt2 = item.ele('@class=Hm4QvXDa')
                        if rank_ele_alt2:
                            hot_rank = rank_ele_alt2.text.strip()
                    
                    # 确定排名索引
                    hot_index = int(hot_rank) if hot_rank.isdigit() else idx + 1
                    logger.debug(f'处理热搜项: 索引={hot_index}, 排名文本={hot_rank}')

                    # 提取热搜标题 - 优先使用h3标签
                    hot_title = item.ele('tag:h3').text.strip() if item.ele('tag:h3') else ''
                    if not hot_title:
                        hot_title = item.ele('@class=title').text.strip()
                    if not hot_title:
                        hot_title = item.ele('@class=OgGNlHqh').text.strip() if item.ele('@class=OgGNlHqh') else ''

                    # 提取热搜描述 - 尝试不同的选择器
                    hot_desc = item.ele('@class=desc').text.strip() if item.ele('@class=desc') else ''

                    # 提取热搜链接 - 尝试不同的选择器
                    hot_url = item.ele('@class=link').attr('href') if item.ele('@class=link') else ''
                    if not hot_url:
                        hot_url = item.ele('@tag()=a').attr('href') if item.ele('@tag()=a') else ''

                    # 提取热搜类型 - 尝试不同的选择器
                    hot_type = item.ele('@class=type').text.strip() if item.ele('@class=type') else ''

                    # 提取热度值 - 使用hot_div.html中的class
                    hot_value = item.ele('@class=WreZoKD3').text.strip() if item.ele('@class=WreZoKD3') else ''
                    if not hot_value:
                        hot_value = item.ele('@class=heat').text.strip() if item.ele('@class=heat') else ''
                    if not hot_value:
                        hot_value = item.ele('@class=W24QxYJb').text.strip() if item.ele('@class=W24QxYJb') else ''

                    hot_search_data.append({
                        'hot_index': hot_index,
                        'hot_title': hot_title,
                        'hot_desc': hot_desc,
                        'hot_url': hot_url,
                        'hot_type': hot_type,
                        'hot_value': hot_value
                    })
                except Exception as e:
                    logger.error(f'提取第 {idx+1} 条热搜数据失败: {str(e)}')
                    continue

            return hot_search_data
        except (ElementLostError, ContextLostError) as e:
            logger.error(f'提取热搜数据失败: {str(e)}')
            raise RuntimeError('热搜数据提取流程中断') from e

    def _save_to_database(self, hot_search_data):
        """保存数据到数据库"""
        try:
            for data in hot_search_data:
                # 检查是否已存在相同标题的热搜
                existing = TiktokHotSearch.objects.filter(hot_title=data['hot_title']).first()
                if existing:
                    # 更新现有记录
                    existing.hot_index = data['hot_index']
                    existing.hot_desc = data['hot_desc']
                    existing.hot_url = data['hot_url']
                    existing.hot_type = data['hot_type']
                    existing.hot_value = data['hot_value']
                    existing.save()
                    logger.info(f'更新热搜: {data["hot_title"]}')
                else:
                    # 创建新记录
                    TiktokHotSearch.objects.create(**data)
                    logger.info(f'新增热搜: {data["hot_title"]}')
        except Exception as e:
            logger.error(f'保存数据到数据库失败: {str(e)}')
            raise RuntimeError('数据库操作失败') from e

    def crawl(self):
        """执行爬取任务"""
        logger.info('开始爬取抖音热搜数据')
        retries = 0
        while retries < self.max_retries:
            try:
                # 打开抖音热搜页面
                self.page.get('https://www.douyin.com/hot', retry=3)
                time.sleep(8)  # 增加等待时间，确保页面完全加载

                # 获取热搜数据
                hot_search_data = self._get_hot_search_data()

                # 保存到数据库
                if hot_search_data:
                    self._save_to_database(hot_search_data)
                    logger.info(f'成功爬取并保存 {len(hot_search_data)} 条热搜数据')
                    return hot_search_data
                else:
                    logger.warning('未获取到热搜数据')
                    return None

            except Exception as e:
                retries += 1
                logger.error(f'第 {retries} 次爬取失败: {str(e)}')
                if retries >= self.max_retries:
                    logger.error('达到最大重试次数，爬取失败')
                    raise
                time.sleep(2)  # 重试前等待2秒
                logger.info(f'准备第 {retries+1} 次爬取...')
        return None

    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'page') and self.page:
                self.page.quit()
                logger.info('浏览器实例已关闭')
        except Exception as e:
            logger.error(f'关闭浏览器实例失败: {str(e)}')

if __name__ == '__main__':
    spider = TiktokHotSearchSpider()
    spider.crawl()
    spider.cleanup()