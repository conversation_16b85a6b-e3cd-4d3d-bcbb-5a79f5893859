<wxs
  src="./index.wxs"
  module="componentUtils"
></wxs>
<label
  class="ant-checkbox-item {{className || ''}}"
  style="{{style || ''}}"
>
  <view class="ant-checkbox-item-container">
    <view class="ant-checkbox-item-wrap">
      <checkbox-group bindchange="onChange">
        <checkbox
          class="ant-checkbox-item-base"
          value="{{value}}"
          checked="{{mixin.value}}"
          disabled="{{disabled}}"
        ></checkbox>
      </checkbox-group>
      <view class="ant-checkbox-item-fake">
        <view
          class="ant-checkbox-item-fake-{{componentUtils.getClassName(mixin.value, disabled)}}"
          style="{{mixin.value && !disabled && color ? 'background:' + color : ''}}"
        >
          <ant-icon
            wx:if="{{mixin.value}}"
            type="CheckOutline"
            className="ant-checkbox-item-fake-{{componentUtils.getClassName(mixin.value, disabled)}}-icon"
          ></ant-icon>
        </view>
      </view>
    </view>
    <view class="ant-checkbox-item-content {{disabled ? 'ant-checkbox-item-disabled' : ''}}">
      <slot></slot>
    </view>
  </view>
</label>