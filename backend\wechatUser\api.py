from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token

from wechatpy import WeChatClient
from django.conf import settings
from .models import WechatUser


class WechatLoginAPI(APIView):
    """微信登录接口（适配Django4.0）"""
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        print(self.headers)
        code = request.data.get('code')
        if not code:
            return Response({"error": "缺少code参数"}, status=400)

        try:
            # 获取微信凭证 
            client = WeChatClient(
                settings.WECHAT_APPID,
                settings.WECHAT_SECRET
            )
            auth_info = client.fetch_access_token()

            # 创建/更新用户 
            user, created = WechatUser.objects.get_or_create(
                username=auth_info['openid'],
                defaults={'is_active': True}
            )

            # 关联微信信息 
            WechatUser.objects.update_or_create(
                user=user,
                defaults={
                    'openid': auth_info['openid'],
                    'session_key': auth_info['session_key'],
                    'unionid': auth_info.get('unionid')
                }
            )

            # 生成访问令牌 
            token, _ = Token.objects.get_or_create(user=user)

            return Response({
                "token": token.key,
                "user_id": user.id
            })

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
