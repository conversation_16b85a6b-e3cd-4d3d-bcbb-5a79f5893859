def generator_drawer(drawer_info):
    return f'''<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="50%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  import {{ defineComponent, ref, computed, unref }} from 'vue';
  import {{ BasicForm, useForm }} from '/@/components/Form/index';
  import {{ BasicDrawer, useDrawerInner }} from '/@/components/Drawer';
  import {{ createOrUpdate }} from './api';
  import {{ formSchema }} from './data';
  import {{ useI18n }} from '/@/hooks/web/useI18n';

  export default defineComponent({{
    name: '{drawer_info.code}Drawer',
    components: {{ BasicDrawer, BasicForm }},
    emits: ['success', 'register'],
    setup(_, {{ emit }}) {{
      const isUpdate = ref(true);
      const {{ t }} = useI18n();

      const [registerForm, {{ resetFields, setFieldsValue, validate }}] = useForm({{
        labelWidth: 100,
        schemas: formSchema,
        showActionButtonGroup: false,
      }});

      const [registerDrawer, {{ setDrawerProps, closeDrawer }}] = useDrawerInner(async (data) => {{
        await resetFields();
        setDrawerProps({{ confirmLoading: false }});
        isUpdate.value = !!data?.isUpdate;

        if (unref(isUpdate)) {{
          await setFieldsValue({{
            ...data.record,
          }});
        }}
      }});

      const getTitle = computed(() =>
        !unref(isUpdate) ? t('common.addText') : t('common.updateText'),
      );

      async function handleSubmit() {{
        try {{
          const values = await validate();
          setDrawerProps({{ confirmLoading: true }});
          await createOrUpdate(values, unref(isUpdate));
          closeDrawer();
          emit('success');
        }} finally {{
          setDrawerProps({{ confirmLoading: false }});
        }}
      }}

      return {{
        registerDrawer,
        registerForm,
        getTitle,
        handleSubmit,
      }};
    }},
  }});
</script>'''
