import random

from DrissionPage import Chromium,ChromiumOptions

class GetTiktokForyou:
    def __init__(self):
        self.options = ChromiumOptions()
        self._browser_start_setup()
        self.chrome = Chromium(self.options)
        self.base_url = 'https://www.tiktok.com/foryou'

    """浏览器启动配置"""
    def _browser_start_setup(self):
        (self.options.headless(False)
        .mute(True)
        .set_argument('--window-size', '1920,1081')
        .set_argument('--start-maximized')  # 设置启动时最大化
        .set_pref('download.prompt_for_download', False)  # 禁用下载弹窗:cite[3]
        .set_local_port(random.randint(1000, 9999)))

    def _get_video_div(self,current_tab):
        print(self.base_url)
        video_div_selector = '@@tag=div@@mode=0'
        current_tab.wait.eles_loaded(video_div_selector, timeout=60)
        current_tab.ele(video_div_selector, timeout=60).r_click()

    def _browse_video_detail(self,current_tab):
        print(self.base_url)
        copy_download_url_selector = '@@tag=a@@href=video/'
        current_tab.wait.eles_loaded(copy_download_url_selector, timeout=60)
        video_url = current_tab.ele(copy_download_url_selector, timeout=60).attr('href')
        print(video_url)

    """获取点赞数标签"""
    def _check_like_counts(self,current_tab):
        print(self.base_url)
        like_counts_selector = '@@tag()=strong@@data-e2e=like-count'
        current_tab.wait.eles_loaded(like_counts_selector, timeout=60)
        like_count_value = current_tab.ele(like_counts_selector,timeout=60).text
        print(like_count_value.find('M'))
        if like_count_value.find('M'):
            self._get_video_div(current_tab)
            self._browse_video_detail(current_tab)
        else:
            print('太少了,看不上')
        print(like_count_value)

    """浏览推荐视频"""
    def browse_foryou(self):
        current_tab = self.chrome.latest_tab
        current_tab.get(self.base_url)
        self._check_like_counts(current_tab)




if __name__ == '__main__':
    get_tiktok_foryou = GetTiktokForyou()
    get_tiktok_foryou.browse_foryou()