import os
import shutil


class FileFolderUtils:
    @staticmethod
    def create_folder(path):
        """创建文件夹（支持递归创建）"""
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建文件夹失败：{str(e)}")
            return False

    @staticmethod
    def create_file(file_path, content=''):
        """创建文件（自动创建父目录）"""
        try:
            dir_path = os.path.dirname(file_path)
            if dir_path and not FileFolderUtils.folder_exists(dir_path):
                FileFolderUtils.create_folder(dir_path)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"创建文件失败：{str(e)}")
            return False

    @staticmethod
    def delete_folder(path):
        """递归删除文件夹及其内容"""
        try:
            if FileFolderUtils.folder_exists(path):
                shutil.rmtree(path)
                return True
            return False
        except Exception as e:
            print(f"删除文件夹失败：{str(e)}")
            return False

    @staticmethod
    def delete_file(file_path):
        """删除文件"""
        try:
            if FileFolderUtils.file_exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            print(f"删除文件失败：{str(e)}")
            return False

    @staticmethod
    def folder_exists(path):
        """检查文件夹是否存在"""
        return os.path.isdir(path)

    @staticmethod
    def file_exists(file_path):
        """检查文件是否存在"""
        return os.path.isfile(file_path)

    @staticmethod
    def clear_folder(path):
        """清空文件夹内容但保留文件夹"""
        try:
            if FileFolderUtils.folder_exists(path):
                for filename in os.listdir(path):
                    file_path = os.path.join(path, filename)
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                return True
            return False
        except Exception as e:
            print(f"清空文件夹失败：{str(e)}")
            return False


# 使用示例
if __name__ == "__main__":
    # 创建测试路径
    test_dir = "test_directory"
    test_file = os.path.join(test_dir, "test_file.txt")

    # 测试文件夹操作
    print(f"创建文件夹：{FileFolderUtils.create_folder(test_dir)}")
    print(f"文件夹存在：{FileFolderUtils.folder_exists(test_dir)}")

    # 测试文件操作
    print(f"创建文件：{FileFolderUtils.create_file(test_file, 'Hello World')}")
    print(f"文件存在：{FileFolderUtils.file_exists(test_file)}")

    # 测试清空文件夹
    print(f"清空文件夹：{FileFolderUtils.clear_folder(test_dir)}")
    print(f"文件存在（清空后）：{FileFolderUtils.file_exists(test_file)}")

    # 测试删除操作
    print(f"删除文件夹：{FileFolderUtils.delete_folder(test_dir)}")
    print(f"文件夹存在（删除后）：{FileFolderUtils.folder_exists(test_dir)}")
